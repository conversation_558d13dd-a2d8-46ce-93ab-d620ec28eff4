{"name": "clubx-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@bprogress/core": "^1.3.4", "@ethersproject/sha2": "^5.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@reown/appkit": "^1.7.11", "@reown/appkit-adapter-ethers": "^1.7.11", "@reown/appkit-adapter-wagmi": "^1.7.11", "@reown/appkit-wallet-button": "^1.7.11", "@tanstack/react-query": "^5.81.2", "@wagmi/core": "^2.17.3", "@walletconnect/ethereum-provider": "^2.21.4", "axios": "^1.7.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "ethers": "^6.13.2", "formik": "^2.4.6", "init": "^0.1.2", "lodash": "^4.17.21", "lucide-react": "^0.440.0", "next": "14.2.5", "next-intl": "^4.1.0", "next-nprogress-bar": "^2.3.13", "react": "^18", "react-dom": "^18", "react-icons": "^5.3.0", "sharp": "^0.33.5", "swiper": "^11.1.9", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "viem": "^2.31.3", "wagmi": "^2.15.6", "yup": "^1.4.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.18", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "null-loader": "^4.0.1", "postcss": "^8", "string-replace-loader": "^3.2.0", "typescript": "^5"}}