import * as Yup from 'yup';

export const DepositValidation = (t: any) => {
  const params = {
    amount: Yup.string().required(t('required')).min(1, t('atleastOne')),
  };
  return Yup.object().shape(params);
};

export const verifyCodeValidation = (t: any) => {
  return Yup.object().shape({
    code: Yup.string().min(2, t('validations.min_code', { value: 2 })),
  });
};

export const emailRegister = (t: any) => {
  return Yup.object({
    email: Yup.string().email(t('enter_valid_email')).required(t('email_address_is_required')),
    password: Yup.string()
      .min(8, t('password_least_8_characters'))
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, t('password_must_contain'))
      .required(t('password_is_required')),
  })
}

export const editEmailValidation = (t: any) => {
  return Yup.object({
    newEmail: Yup.string()
      .email(t('my_page.email_not_verified'))
      .required(t('my_page.enter_your_email')),
  })
}

export const editPassword = (t: any) => {
  return Yup.object({
    currentPassword: Yup.string().required(t('my_page.enter_your_current_password')),
    newPassword: Yup.string()
      .min(6, t('my_page.enter_password_least_6_characters'))
      .required(t('my_page.enter_new_passwrod')),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref("newPassword")], t('my_page.new_password_not_match'))
      .required(t('my_page.enter_new_passwrod')),
  })
}