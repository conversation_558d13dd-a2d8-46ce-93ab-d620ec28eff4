[{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}, {"indexed": false, "internalType": "contract IERC20", "name": "_ercToken", "type": "address"}], "name": "ClubXDepositEevnt", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "deposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]