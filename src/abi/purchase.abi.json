[{"inputs": [{"internalType": "uint256[]", "name": "tokenIds", "type": "uint256[]"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "deposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "_data", "type": "bytes"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "purchase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "_data", "type": "bytes"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "payable", "type": "function"}]