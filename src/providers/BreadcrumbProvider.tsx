"use client";
import { createContext, useContext, ReactNode, useState } from 'react';

type BreadcrumbContextType = {
  setBreadcrumb: (crumb: ReactNode) => void;
};

const BreadcrumbContext = createContext<BreadcrumbContextType | undefined>(undefined);

export const useBreadcrumb = () => {
  const context = useContext(BreadcrumbContext);
  if (!context) throw new Error("useBreadcrumb must be used within BreadcrumbProvider");
  return context;
};

const BreadcrumbProvider = ({ children }: { children: React.ReactNode }) => {
  const [breadcrumb, setBreadcrumb] = useState<ReactNode>(null);
  return (
    <BreadcrumbContext.Provider value={{ setBreadcrumb }}>
      {
        breadcrumb && (
          <div className='w-full text-[#6D6D6D] text-sm px-5'>
            <div className='flex items-center gap-8 overflow-auto'>
              {breadcrumb}
            </div>
          </div>
        )
      }
      {children}
    </BreadcrumbContext.Provider>
  );
};
export default BreadcrumbProvider;
