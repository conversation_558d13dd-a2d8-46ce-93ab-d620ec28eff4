'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'

export function StorageBlocker() {
  const pathname = usePathname()

  useEffect(() => {
    if (typeof window === 'undefined') return

    // Check if we're on the login page
    const isLoginPage = pathname?.includes('/auth/login')

    // Store original methods
    const originalSetItem = window.localStorage.setItem
    const originalGetItem = window.localStorage.getItem
    const originalRemoveItem = window.localStorage.removeItem

    // List of keys to block
    const blockedPatterns = [
      '@appkit/',
      '@w3m/',
      'wc@2:',
      'wagmi.',
      '-walletlink',
      'reown',
      'walletconnect'
    ]

    // Override setItem to block wallet-related keys on login page
    window.localStorage.setItem = function(key: string, value: string) {
      // Check if key should be blocked (only on login page)
      const shouldBlock = isLoginPage && blockedPatterns.some(pattern => key.includes(pattern))

      if (shouldBlock) {
        console.log('[StorageBlocker] Blocked localStorage.setItem:', key)
        return
      }

      // Allow other keys
      return originalSetItem.call(this, key, value)
    }

    // Override getItem to return null for blocked keys on login page
    window.localStorage.getItem = function(key: string) {
      // Check if key should be blocked (only on login page)
      const shouldBlock = isLoginPage && blockedPatterns.some(pattern => key.includes(pattern))

      if (shouldBlock) {
        console.log('[StorageBlocker] Blocked localStorage.getItem:', key)
        return null
      }

      // Allow other keys
      return originalGetItem.call(this, key)
    }

    // Clean up any existing blocked keys on mount (only on login page)
    if (isLoginPage) {
      const allKeys = Object.keys(localStorage)
      allKeys.forEach(key => {
        const shouldRemove = blockedPatterns.some(pattern => key.includes(pattern))
        if (shouldRemove) {
          console.log('[StorageBlocker] Removing existing key:', key)
          originalRemoveItem.call(localStorage, key)
        }
      })

      // Also clean sessionStorage
      const sessionKeys = Object.keys(sessionStorage)
      sessionKeys.forEach(key => {
        const shouldRemove = blockedPatterns.some(pattern => key.includes(pattern))
        if (shouldRemove) {
          console.log('[StorageBlocker] Removing session key:', key)
          sessionStorage.removeItem(key)
        }
      })
    }

    // Cleanup function to restore original methods
    return () => {
      window.localStorage.setItem = originalSetItem
      window.localStorage.getItem = originalGetItem
      window.localStorage.removeItem = originalRemoveItem
    }
  }, [pathname])

  return null
}