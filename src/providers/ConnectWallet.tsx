"use client";

import React from 'react';
import { WagmiProvider } from 'wagmi'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { wagmiAdapter } from '@/config/appkit'
import { walletConfig } from '@/config/wallet-config'
import { appKitInstance } from '@/config/appkit'

// Create query client
const queryClient = new QueryClient();

function ConnectWalletProvider({ children }: { children: React.ReactNode }) {
  // appKit instance is already created in the config file
  
  return (
    <WagmiProvider 
      config={wagmiAdapter.wagmiConfig} 
      reconnectOnMount={walletConfig.reconnectOnMount}
    >
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </WagmiProvider>
  );
}

export default ConnectWalletProvider;
