'use client'

import React from 'react'
import ConnectWalletProvider from './ConnectWallet'

/**
 * Always provides wallet context but with route-aware configuration
 */
export function ConditionalWalletProvider({ children }: { children: React.ReactNode }) {
  // Always provide the full wallet provider
  // The blocking logic is handled at component level
  return (
    <ConnectWalletProvider>
      {children}
    </ConnectWalletProvider>
  )
}