import { <PERSON><PERSON><PERSON><PERSON>rovider } from 'ethers';

export enum WalletType {
  METAMASK = 'metamask',
  WALLETCONNECT = 'walletconnect',
  TRUST = 'trust',
  COINBASE = 'coinbase',
}

export interface WalletInfo {
  address: string;
  chainId: string;
  provider: <PERSON><PERSON>er<PERSON>rovider;
}

export class WalletService {
  private static instance: WalletService;

  private constructor() {}

  static getInstance(): WalletService {
    if (!WalletService.instance) {
      WalletService.instance = new WalletService();
    }
    return WalletService.instance;
  }

  async connectMetaMask(): Promise<WalletInfo> {
    if (typeof window === 'undefined' || !window.ethereum) {
      throw new Error('MetaMask is not installed');
    }

    try {
      const accounts = await (window.ethereum as any).request({
        method: 'eth_requestAccounts',
      }) as string[];

      if (!accounts || accounts.length === 0) {
        throw new Error('No accounts found');
      }

      const chainId = await (window.ethereum as any).request({
        method: 'eth_chainId',
      }) as string;

      const provider = new BrowserProvider(window.ethereum as any);

      return {
        address: accounts[0],
        chainId,
        provider,
      };
    } catch (error: any) {
      if (error.code === 4001) {
        throw new Error('User rejected the connection request');
      } else if (error.code === -32002) {
        throw new Error('A connection request is already pending');
      }
      throw error;
    }
  }

  async connectTrustWallet(): Promise<WalletInfo> {
    // Trust Wallet uses the same injection as MetaMask on mobile
    // On desktop, it can use WalletConnect
    if (typeof window !== 'undefined' && this.isMobile() && window.ethereum) {
      return this.connectMetaMask();
    }
    throw new Error('Trust Wallet connection requires WalletConnect');
  }

  async connectCoinbaseWallet(): Promise<WalletInfo> {
    // Check if Coinbase Wallet is injected
    if (typeof window !== 'undefined' && window.ethereum && window.ethereum.isCoinbaseWallet) {
      return this.connectMetaMask();
    }
    throw new Error('Coinbase Wallet is not available');
  }

  async switchNetwork(chainId: string): Promise<void> {
    if (typeof window === 'undefined' || !window.ethereum) {
      throw new Error('No wallet provider found');
    }

    try {
      await (window.ethereum as any).request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId }],
      }) as void;
    } catch (error: any) {
      if (error.code === 4902) {
        throw new Error('This network is not added to your wallet');
      }
      throw error;
    }
  }

  async addNetwork(networkParams: {
    chainId: string;
    chainName: string;
    rpcUrls: string[];
    nativeCurrency: {
      name: string;
      symbol: string;
      decimals: number;
    };
    blockExplorerUrls?: string[];
  }): Promise<void> {
    if (typeof window === 'undefined' || !window.ethereum) {
      throw new Error('No wallet provider found');
    }

    try {
      await (window.ethereum as any).request({
        method: 'wallet_addEthereumChain',
        params: [networkParams],
      }) as void;
    } catch (error) {
      throw new Error('Failed to add network');
    }
  }

  async signMessage(provider: BrowserProvider, message: string, address: string): Promise<string> {
    const signer = await provider.getSigner();
    return await signer.signMessage(message);
  }

  async isWalletInstalled(walletType: WalletType): Promise<boolean> {
    if (typeof window === 'undefined') return false;

    switch (walletType) {
      case WalletType.METAMASK:
        return !!(window.ethereum && window.ethereum.isMetaMask);
      case WalletType.TRUST:
        return !!(window.ethereum && window.ethereum.isTrust);
      case WalletType.COINBASE:
        return !!(window.ethereum && window.ethereum.isCoinbaseWallet);
      case WalletType.WALLETCONNECT:
        return true; // WalletConnect is always available
      default:
        return false;
    }
  }

  private isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  }

  getWalletName(walletType: WalletType): string {
    switch (walletType) {
      case WalletType.METAMASK:
        return 'MetaMask';
      case WalletType.WALLETCONNECT:
        return 'WalletConnect';
      case WalletType.TRUST:
        return 'Trust Wallet';
      case WalletType.COINBASE:
        return 'Coinbase Wallet';
      default:
        return 'Unknown Wallet';
    }
  }
}