/**
 * Clears all wallet-related data from localStorage
 * This ensures complete disconnection and prevents auto-reconnection
 */
export function clearAllWalletStorage(): void {
  if (typeof window === 'undefined') return;

  try {
    // Clear wagmi storage
    localStorage.removeItem('wagmi.store');
    localStorage.removeItem('wagmi.recentConnectorId');
    localStorage.removeItem('wagmi.wallet');
    localStorage.removeItem('wagmi.connected');
    
    // Clear WalletConnect storage (v2 and legacy)
    const walletConnectKeys = Object.keys(localStorage).filter(key => 
      key.startsWith('wc@') || 
      key.startsWith('walletconnect') ||
      key.startsWith('WC_') ||
      key.includes('-walletlink') ||
      key.startsWith('reown')
    );
    
    walletConnectKeys.forEach(key => {
      localStorage.removeItem(key);
    });

    // Clear Reown AppKit storage
    localStorage.removeItem('@w3m/recent');
    localStorage.removeItem('@w3m/connected_connector');
    localStorage.removeItem('@w3m/wallet_id');
    localStorage.removeItem('@w3m/active_caip_network');
    localStorage.removeItem('@w3m/connected_wallet_image_url');
    localStorage.removeItem('@w3m/email');
    localStorage.removeItem('@w3m/email_connector');
    
    // Clear modal state
    localStorage.removeItem('@appkit/active_caip_network');
    localStorage.removeItem('@appkit/connected_connector');
    localStorage.removeItem('@appkit/recent');
    localStorage.removeItem('@appkit/wallet_id');
    
    // Clear any session storage as well
    if (sessionStorage) {
      const sessionKeys = Object.keys(sessionStorage).filter(key =>
        key.includes('wallet') || 
        key.includes('wagmi') || 
        key.startsWith('wc') ||
        key.startsWith('reown')
      );
      
      sessionKeys.forEach(key => {
        sessionStorage.removeItem(key);
      });
    }
  } catch (error) {
    console.error('Error clearing wallet storage:', error);
  }
}