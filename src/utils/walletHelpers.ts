// Helper to get the correct ethereum provider when multiple wallets are installed
export function getMetaMaskProvider() {
  if (typeof window === 'undefined' || !window.ethereum) {
    return null
  }

  // If ethereum.providers exists, multiple wallets are installed
  if ((window.ethereum as any).providers?.length) {
    // Find MetaMask provider
    const metaMaskProvider = (window.ethereum as any).providers.find(
      (provider: any) => provider.isMetaMask && !provider.isBraveWallet
    )
    return metaMaskProvider || null
  }

  // Single provider
  if ((window.ethereum as any).isMetaMask && !(window.ethereum as any).isBraveWallet) {
    return window.ethereum
  }

  return null
}

export function getTrustWalletProvider() {
  if (typeof window === 'undefined' || !window.ethereum) {
    return null
  }

  // If ethereum.providers exists, multiple wallets are installed
  if ((window.ethereum as any).providers?.length) {
    // Find Trust Wallet provider
    const trustProvider = (window.ethereum as any).providers.find(
      (provider: any) => provider.isTrust
    )
    return trustProvider || null
  }

  // Single provider
  if ((window.ethereum as any).isTrust) {
    return window.ethereum
  }

  return null
}