/**
 * Force close any open wallet modals and clear connection state
 * This is a more aggressive approach to ensure modals don't auto-open
 */
export function forceCloseWalletModal() {
  if (typeof window === 'undefined') return;

  try {
    // Clear all possible wallet modal states
    const keysToRemove = [
      // AppKit/Reown modal states
      '@w3m/modal_open',
      '@w3m/active_view', 
      '@w3m/pending_connection',
      '@w3m/connected_connector',
      '@w3m/recent',
      '@w3m/wallet_id',
      '@w3m/connected_wallet_image_url',
      '@w3m/active_caip_network',
      '@appkit/modal_open',
      '@appkit/active_view',
      '@appkit/pending_connection',
      '@appkit/connected_connector',
      '@appkit/recent',
      '@appkit/wallet_id',
      
      // WalletConnect states
      'wc@2:client:0.3//session',
      'wc@2:core:0.3//messages',
      'wc@2:core:0.3//keychain',
      'wc@2:core:0.3//expirer',
      'wc@2:core:0.3//history',
      'wc@2:core:0.3//pairing',
      
      // Wagmi states
      'wagmi.store',
      'wagmi.cache',
      'wagmi.recentConnectorId',
      'wagmi.wallet',
      'wagmi.connected',
      'wagmi.connecting',
      'wagmi.disconnecting',
      'wagmi.reconnecting',
    ];

    // Remove all keys
    keysToRemove.forEach(key => {
      try {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
      } catch (e) {
        // Ignore errors for non-existent keys
      }
    });

    // Also clear any keys that match patterns
    const patterns = ['@w3m', '@appkit', 'wc@', 'wagmi', 'reown', 'walletconnect'];
    
    // Clear from localStorage
    Object.keys(localStorage).forEach(key => {
      if (patterns.some(pattern => key.includes(pattern))) {
        localStorage.removeItem(key);
      }
    });

    // Clear from sessionStorage
    Object.keys(sessionStorage).forEach(key => {
      if (patterns.some(pattern => key.includes(pattern))) {
        sessionStorage.removeItem(key);
      }
    });

    console.log('Wallet modal state forcefully cleared');
  } catch (error) {
    console.error('Error clearing wallet modal state:', error);
  }
}