import { base } from 'viem/chains';

export const setupNetwork = async (provider: any, chainId: any) => {
  const chainNumber = `0x${Number(chainId).toString(16)}`;
  const chains = [base];
  const chain: any = chains.find((obj: any) => obj.id === chainId);
  console.log(chain)
  try {
    await provider.send("wallet_switchEthereumChain", [
      { chainId: chainNumber },
    ]);
  } catch (switchError: any) {
    // This error code indicates that the chain has not been added to MetaMask.
    if (switchError.code === 4902) {
      try {
        await provider.send("wallet_addEthereumChain", [
          {
            chainId: chainNumber,
            chainName: chain?.name,
            rpcUrls: chain?.rpcUrls?.default || [],
            nativeCurrency: {
              name: "ETH",
              symbol: "ETH",
              decimals: 18,
            },
            blockExplorerUrls: [chain?.blockExplorers.default.url],
          },
        ]);
      } catch (addError) {
        throw addError;
      }
    }
    // handle other "switch" errors
    console.error(switchError);
    throw switchError;
  }
};
