'use client'

import { useCallback } from 'react'
import { useAppKit } from '@reown/appkit/react'
import { useConnect } from 'wagmi'

export function useTrustWalletButton() {
  const { open } = useAppKit()
  const { connectors, connect } = useConnect()

  const handleTrustWalletClick = useCallback(async () => {
    console.log('[useTrustWalletButton] Trust Wallet clicked by user');
    
    // Clear any blocking states before attempting connection
    const clearTrustBlockingStates = () => {
      localStorage.removeItem('trust.disconnected');
      localStorage.removeItem('wagmi.com.trustwallet.app.disconnected');
      localStorage.removeItem('@appkit/wallet_id');
      
      // Set a flag to indicate user action
      localStorage.setItem('trust_user_clicked', 'true');
      setTimeout(() => {
        localStorage.removeItem('trust_user_clicked');
      }, 5000);
    };
    
    clearTrustBlockingStates();
    
    try {
      // First try to find Trust Wallet connector
      const trustConnector = connectors.find(c => 
        c.name?.toLowerCase().includes('trust') || 
        c.id.toLowerCase().includes('trust')
      );
      
      if (trustConnector) {
        console.log('[useTrustWalletButton] Found Trust connector:', trustConnector.name);
        await connect({ connector: trustConnector });
      } else {
        // Open AppKit modal and let user select Trust Wallet
        console.log('[useTrustWalletButton] Opening modal for Trust Wallet selection');
        
        // Small delay to ensure blocking states are cleared
        setTimeout(async () => {
          await open({ view: 'Connect' });
        }, 100);
      }
    } catch (error) {
      console.error('[useTrustWalletButton] Error:', error);
      // Fallback to opening modal
      setTimeout(async () => {
        await open({ view: 'Connect' });
      }, 100);
    }
  }, [connectors, connect, open]);
  
  return { handleTrustWalletClick };
}