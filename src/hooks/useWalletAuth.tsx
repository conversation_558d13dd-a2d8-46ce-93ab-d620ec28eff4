import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import CryptoJS from 'crypto-js';
import { useAppKitAccount, useAppKitProvider } from '@reown/appkit/react';
import { BrowserProvider } from 'ethers';
import * as AuthAction from '@/actions/auth';
import { handleStartGame } from '@/actions/games';
import { decryptKey } from '@/lib/encryptKey';
import useSession from '@/components/session/useSession';
import { useToast } from '@/components/ui/useToast';

interface UseWalletAuthProps {
  callback?: string;
  referralCode?: string;
}

export const useWalletAuth = ({ callback, referralCode }: UseWalletAuthProps = {}) => {
  const { address, isConnected, status } = useAppKitAccount();
  const { walletProvider } = useAppKitProvider('eip155');
  const router = useRouter();
  const t = useTranslations('');
  const { toast } = useToast();
  const { setSession } = useSession();

  useEffect(() => {
    const authenticateWallet = async () => {
      if (isConnected && address && walletProvider && status === 'connected') {
        try {
          // Create ethers provider
          const provider = new BrowserProvider(walletProvider as any);
          const signer = await provider.getSigner();

          // Get nonce
          const nounParams = { address } as any;
          if (referralCode) nounParams.code = referralCode;

          const { data, status: nounStatus, message: nounMessage } = await AuthAction.fetchNounce(nounParams);

          if (nounStatus !== 'success') {
            throw new Error(nounMessage || 'Failed to fetch nonce');
          }

          const nounce = data?.nounce;

          // Create signature message
          const preMessage = `Welcome to Club X!\n\nClick "Connect" to integrate your wallet with your account.\n\nI accept the Club X Terms of Service: https://clubx.tech/terms\n\nWallet address:\n${address}\nNounce:\n${nounce}`;
          const reference = CryptoJS.MD5(`${preMessage}_${nounce}-${address?.toLowerCase()}`).toString();
          const signatureMessage = `Welcome to Club X!\n\nClick "Connect" to integrate your wallet with your account.\n\nI accept the Club X Terms of Service: https://clubx.tech/terms\n\nWallet address:\n${address}\nNounce:\n${nounce}\nReference\n${reference}`;

          // Sign message
          const signature = await signer.signMessage(signatureMessage);

          // Authenticate
          const { status, data: resp, message } = await AuthAction.fetchWallets(address!, signature);

          if (status === 'success') {
            // Handle callback if present
            if (callback) {
              const id = decryptKey(callback!);
              if (id) {
                const { status: gameStatus, url } = await handleStartGame(id);
                if (gameStatus !== 'success') {
                  toast({
                    variant: 'error',
                    description: t('startGameError'),
                  });
                  return;
                }
                toast({
                  variant: 'success',
                  description: resp.message,
                });
                router.push('/');
                window.open(url, '_blank');
                return;
              }
            }

            // Normal login success
            toast({
              variant: 'success',
              description: resp.message,
            });
            router.push('/');
            setSession(resp.user);
          } else {
            throw new Error(message || 'Authentication failed');
          }
        } catch (error: any) {
          console.error('Wallet authentication error:', error);

          let errorMessage = 'Failed to authenticate wallet';

          if (error.message.includes('rejected')) {
            errorMessage = 'You rejected the signature request.';
          } else if (error.message) {
            errorMessage = error.message;
          }

          toast({
            variant: 'error',
            description: errorMessage,
          });
        }
      }
    };

    authenticateWallet();
  }, [isConnected, address, walletProvider, status, callback, referralCode, router, t, toast, setSession]);

  return {
    isConnected,
    address,
    isAuthenticating: isConnected && status === 'connecting'
  };
};