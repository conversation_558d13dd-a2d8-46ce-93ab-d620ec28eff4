import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import CryptoJS from 'crypto-js';
import { useAppKit, useAppKitAccount, useAppKitProvider } from '@reown/appkit/react';
import { useDisconnect } from 'wagmi';
import { BrowserProvider } from 'ethers';
import { WalletService, WalletType, WalletInfo } from '@/services/wallet.service';
import * as AuthAction from '@/actions/auth';
import { handleStartGame } from '@/actions/games';
import { decryptKey } from '@/lib/encryptKey';
import useSession from '@/components/session/useSession';
import { useToast } from '@/components/ui/useToast';
import { clearAllWalletStorage } from '@/utils/clearWalletStorage';

interface UseWalletConnectionProps {
  callback?: string;
  referralCode?: string;
}

export const useWalletConnection = ({ callback, referralCode }: UseWalletConnectionProps = {}) => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectedWallet, setConnectedWallet] = useState<WalletInfo | null>(null);
  const router = useRouter();
  const t = useTranslations('');
  const { toast } = useToast();
  const { setSession } = useSession();
  const { open } = useAppKit();
  const { address, isConnected, caipAddress } = useAppKitAccount();
  const { walletProvider } = useAppKitProvider('eip155');
  const { disconnect } = useDisconnect();
  const walletService = WalletService.getInstance();

  const handleAuthentication = async (walletInfo: WalletInfo) => {
    try {
      // Get nonce
      const nounParams = { address: walletInfo.address } as any;
      if (referralCode) nounParams.code = referralCode;

      const { data, status: nounStatus, message: nounMessage } = await AuthAction.fetchNounce(nounParams);
      
      if (nounStatus !== 'success') {
        throw new Error(nounMessage || 'Failed to fetch nonce');
      }

      const nounce = data?.nounce;
      
      // Create signature message
      const preMessage = `Welcome to Club X!\n\nClick "Connect" to integrate your wallet with your account.\n\nI accept the Club X Terms of Service: https://clubx.tech/terms\n\nWallet address:\n${walletInfo.address}\nNounce:\n${nounce}`;
      const reference = CryptoJS.MD5(`${preMessage}_${nounce}-${walletInfo.address?.toLowerCase()}`).toString();
      const signatureMessage = `Welcome to Club X!\n\nClick "Connect" to integrate your wallet with your account.\n\nI accept the Club X Terms of Service: https://clubx.tech/terms\n\nWallet address:\n${walletInfo.address}\nNounce:\n${nounce}\nReference\n${reference}`;

      // Sign message
      const signature = await walletService.signMessage(walletInfo.provider, signatureMessage, walletInfo.address);

      // Authenticate
      const { status, data: resp, message } = await AuthAction.fetchWallets(walletInfo.address, signature);
      
      if (status === 'success') {
        // Handle callback if present
        if (callback) {
          const id = decryptKey(callback);
          if (id) {
            const { status: gameStatus, url } = await handleStartGame(id);
            if (gameStatus !== 'success') {
              toast({
                variant: 'error',
                description: t('startGameError'),
              });
              return;
            }
            toast({
              variant: 'success',
              description: resp.message,
            });
            router.push('/');
            window.open(url, '_blank');
            return;
          }
        }

        // Normal login success
        toast({
          variant: 'success',
          description: resp.message,
        });
        router.push('/');
        setSession(resp.user);
      } else {
        throw new Error(message || 'Authentication failed');
      }
    } catch (error: any) {
      console.error('Authentication error:', error);
      throw error;
    }
  };

  const connectWallet = useCallback(async (walletType: WalletType) => {
    console.log('connectWallet called with type:', walletType);
    setIsConnecting(true);
    try {
      let walletInfo: WalletInfo;

      switch (walletType) {
        case WalletType.METAMASK:
          console.log('Connecting to MetaMask...');
          walletInfo = await walletService.connectMetaMask();
          break;
        case WalletType.TRUST:
          console.log('Connecting to Trust Wallet...');
          walletInfo = await walletService.connectTrustWallet();
          break;
        case WalletType.COINBASE:
          console.log('Connecting to Coinbase Wallet...');
          walletInfo = await walletService.connectCoinbaseWallet();
          break;
        case WalletType.WALLETCONNECT:
          console.log('Opening WalletConnect modal...');
          // Open AppKit modal for WalletConnect
          await open({ view: 'Connect' });
          setIsConnecting(false);
          return;
        default:
          throw new Error('Unsupported wallet type');
      }

      setConnectedWallet(walletInfo);
      await handleAuthentication(walletInfo);
    } catch (error: any) {
      console.error('Wallet connection error:', error);
      console.error('Error stack:', error.stack);
      
      // User-friendly error messages
      let errorMessage = 'Failed to connect wallet';
      
      if (error.message.includes('not installed')) {
        errorMessage = `${walletService.getWalletName(walletType)} is not installed. Please install it to continue.`;
      } else if (error.message.includes('rejected')) {
        errorMessage = 'You rejected the connection request.';
      } else if (error.message.includes('pending')) {
        errorMessage = 'A connection request is already pending. Please check your wallet.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        variant: 'error',
        description: errorMessage,
      });
    } finally {
      setIsConnecting(false);
    }
  }, [walletService, open, toast]);

  const disconnectWallet = useCallback(async () => {
    try {
      await disconnect();
      setConnectedWallet(null);
      
      // Clear all wallet storage to ensure clean disconnection
      clearAllWalletStorage();
    } catch (error) {
      console.error('Error disconnecting wallet:', error);
    }
  }, [disconnect]);

  const checkWalletAvailability = useCallback(async (walletType: WalletType): Promise<boolean> => {
    return await walletService.isWalletInstalled(walletType);
  }, [walletService]);

  // Handle WalletConnect authentication when connected
  useEffect(() => {
    const handleWalletConnectAuth = async () => {
      console.log('WalletConnect auth check - isConnected:', isConnected, 'address:', address);
      if (isConnected && address && walletProvider) {
        try {
          setIsConnecting(true);
          const provider = new BrowserProvider(walletProvider as any);
          const network = await provider.getNetwork();
          
          const walletInfo: WalletInfo = {
            address,
            chainId: network.chainId.toString(),
            provider
          };
          
          setConnectedWallet(walletInfo);
          await handleAuthentication(walletInfo);
        } catch (error) {
          console.error('WalletConnect authentication error:', error);
          toast({
            variant: 'error',
            description: 'Failed to authenticate with WalletConnect',
          });
        } finally {
          setIsConnecting(false);
        }
      }
    };

    if (isConnected && !connectedWallet) {
      handleWalletConnectAuth();
    }
  }, [isConnected, address, walletProvider, connectedWallet, handleAuthentication, toast]);

  return {
    connectWallet,
    disconnectWallet,
    isConnecting,
    connectedWallet,
    checkWalletAvailability,
    isWalletConnected: isConnected,
    walletAddress: address
  };
};