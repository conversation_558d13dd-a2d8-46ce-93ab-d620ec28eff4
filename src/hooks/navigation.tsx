import { useCallback, useMemo } from "react";
import { defineRouting } from "next-intl/routing";
import { createNavigation } from "next-intl/navigation";
import { BProgress, isSameURL } from "@bprogress/core";
import "@bprogress/core/css";
import { NavigateOptions } from "next/dist/shared/lib/app-router-context.shared-runtime";

export const locales = ["us", "jp"] as const;
export type LocaleType = "us" | "jp";

type OptionType = NavigateOptions & {
  locale?: LocaleType;
};

interface RouterNProgressOptions {
  showProgressBar?: boolean;
  startPosition?: number;
  disableSameURL?: boolean;
}

export const routing = defineRouting({
  locales,
  localePrefix: "as-needed",
  defaultLocale: "us",
});

const navigation = createNavigation(routing);

export function useRouter() {
  const router = navigation.useRouter();

  const startProgress = useCallback(
    (startPosition?: number) => {
      if (startPosition && startPosition > 0) BProgress.set(startPosition);
      BProgress.start();
    },
    [router]
  );

  const progress = useCallback(
    (
      href: string,
      options?: OptionType,
      NProgressOptions?: RouterNProgressOptions
    ) => {
      if (NProgressOptions?.showProgressBar === false) {
        return router.push(href, options);
      }

      const currentUrl = new URL(location.href);
      const targetUrl = new URL(href, location.href);

      if (
        isSameURL(targetUrl, currentUrl) &&
        NProgressOptions?.disableSameURL !== false
      )
        return router.push(href, options);

      startProgress(NProgressOptions?.startPosition);
    },
    [router]
  );

  const push = useCallback(
    (
      href: string,
      options?: OptionType,
      NProgressOptions?: RouterNProgressOptions
    ) => {
      progress(href, options, NProgressOptions);
      return router.push(href, options);
    },
    [router, startProgress]
  );

  const replace = useCallback(
    (
      href: string,
      options?: OptionType,
      NProgressOptions?: RouterNProgressOptions
    ) => {
      progress(href, options, NProgressOptions);
      return router.replace(href, options);
    },
    [router, startProgress]
  );

  const back = useCallback(
    (NProgressOptions?: RouterNProgressOptions) => {
      if (NProgressOptions?.showProgressBar === false) return router.back();

      startProgress(NProgressOptions?.startPosition);

      return router.back();
    },
    [router]
  );

  const enhancedRouter = useMemo(() => {
    return { ...router, push, replace, back };
  }, [router, push, replace, back]);

  return enhancedRouter;
}

export const { Link, redirect, usePathname, getPathname } = navigation;
