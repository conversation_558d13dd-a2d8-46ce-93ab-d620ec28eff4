import type { <PERSON>ada<PERSON> } from "next";
import { Heeb<PERSON> } from "next/font/google";
import "./globals.css";

const heebo = Heebo({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  variable: '--font-heebo',
});

const title = "ClubX"
const description = "ClubX"

export const metadata: Metadata = {
  title,
  description,
  openGraph: {
    title,
    description,
    type: 'website'
  },
  twitter: {
    title,
    description,
    card: 'summary_large_image'
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <script src="/prevent-wallet-modal.js" />
      </head>
      <body className={heebo.className}>{children}</body>
    </html>
  );
}
