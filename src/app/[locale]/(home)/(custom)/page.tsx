import { fetchBanner, fetchCategories, fetchChains, fetchHome, fetchPickUpGames } from '@/actions/home';
import HomePage from '@/components/home/<USER>';

const Home = async ({ params }: any) => {
  const { banners } = await fetchBanner(params);
  const { pickUpGames } = await fetchPickUpGames(params);
  const { data } = await fetchHome();
  const { data: categories } = await fetchCategories({});
  const { data: chains } = await fetchChains({});
  return (
    <HomePage bannersData={banners} pickUpGameData={pickUpGames} homeData={data} categories={categories} chains={chains}  />
  )

};

export default Home;
