import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Category from '@/components/categories/Container';
import { fetchChains, fetchGamesChains } from '@/actions/home';

export async function generateMetadata({ params }: any): Promise<Metadata> {
  const title = `${params?.slug}-ClubX`;
  const description = `${params?.slug}-ClubX`;
  const image = '';

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
      images: image,
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: image,
    },
  };
}

const page = async ({ params }: any) => {
  const param = await params
  const { data: chains } = await fetchChains({});
  const { data, meta, status } = await fetchGamesChains({ slug: param?.slug });

  if (status !== "success") notFound();

  return <Category data={data} meta={meta} slug={param?.slug} typeData={chains} type="chain" />
}

export default page