import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { fetchLatestGames } from '@/actions/home';
import EventsPage from '@/components/event-page/page';

const title = "Upcoming-ClubX"
const description = "Upcoming-ClubX"

export const metadata: Metadata = {
  title,
  description,
  openGraph: {
    title,
    description,
    type: 'website',
    images: ''
  },
  twitter: {
    title,
    description,
    card: 'summary_large_image',
    images: ''
  }
};

const page = async () => {
  const { data, meta, status } = await fetchLatestGames({});

  if (status !== "success") notFound();

  return <EventsPage type='latest' data={data} meta={meta} />
}

export default page