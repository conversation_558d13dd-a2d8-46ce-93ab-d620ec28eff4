import { fetchCategories, fetchChains } from '@/actions/home';
import GridLayout from '@/components/layout/grid-layout';

const layout = async ({ children }: { children: React.ReactNode; }) => {
  const { data: categories } = await fetchCategories({});
  const { data: chains } = await fetchChains({});
  return (
    <GridLayout categories={categories} chains={chains}>
      {children}
    </GridLayout>
  )
}

export default layout