import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Category from '@/components/categories/Container';
import { fetchCategories, fetchChains, fetchGamesCategories } from '@/actions/home';

export async function generateMetadata({ params }: any): Promise<Metadata> {
  const title = `${params?.slug}-ClubX`;
  const description = `${params?.slug}-ClubX`;
  const image = '';

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
      images: image,
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: image,
    },
  };
}

const page = async ({ params }: any) => {
  const param = await params
  const { data: categories } = await fetchCategories({});
  const { data, meta, status } = await fetchGamesCategories({ slug: param?.slug });
  const { data: chains } = await fetchChains({});

  if (status !== "success") notFound();

  return <Category data={data} meta={meta} slug={param?.slug} typeData={categories} type="category" chains={chains} />
}

export default page