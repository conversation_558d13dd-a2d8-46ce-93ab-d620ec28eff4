import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { fetchEvents } from '@/actions/home';
import EventsPage from '@/components/event-page/page';

const title = "Events-ClubX"
const description = "Events-ClubX"

export const metadata: Metadata = {
  title,
  description,
  openGraph: {
    title,
    description,
    type: 'website',
    images: ''
  },
  twitter: {
    title,
    description,
    card: 'summary_large_image',
    images: ''
  }
};

const page = async () => {
  const { data, meta, status } = await fetchEvents({});

  if (status !== "success") notFound();

  return <EventsPage type='events' data={data} meta={meta} />
}

export default page