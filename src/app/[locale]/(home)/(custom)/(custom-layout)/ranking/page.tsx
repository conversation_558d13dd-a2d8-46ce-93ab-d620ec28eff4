import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Ranking from '@/components/ranking/container';
import { fetchCategories, fetchRanking } from '@/actions/home';

const title = "Ranking-ClubX"
const description = "Ranking-ClubX"

export const metadata: Metadata = {
  title,
  description,
  openGraph: {
    title,
    description,
    type: 'website',
    images: ''
  },
  twitter: {
    title,
    description,
    card: 'summary_large_image',
    images: ''
  }
};

const page = async () => {
  const { data, meta, status } = await fetchRanking({});
  const { data: categories } = await fetchCategories({});
  if (status !== "success") notFound();

  return <Ranking data={data} meta={meta} categories={categories} />
}

export default page