import { Metadata } from 'next';
import Policy from '@/components/policy/page'

const title = "Policy-ClubX"
const description = "Policy-ClubX"

export const metadata: Metadata = {
  title,
  description,
  openGraph: {
    title,
    description,
    type: 'website',
    images: ''
  },
  twitter: {
    title,
    description,
    card: 'summary_large_image',
    images: ''
  }
};

const page = () => {
  return <Policy />
}

export default page