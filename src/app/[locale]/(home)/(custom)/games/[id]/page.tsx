import { Metadata } from 'next';
import { fetchGame } from '@/actions/games'
import GameDetail from '@/components/game-detail/container'

export async function generateMetadata({ params }: any): Promise<Metadata> {
  const { data } = await fetchGame(params?.id);
  const title = data?.name?.jp || data?.name?.en;
  const description = data?.description;
  const image = data?.image;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
      images: image,
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image',
      images: image,
    },
  };
}

const page = async ({ params }: any) => {
  const param = await params
  const resp = await fetchGame(param?.id)
  return <GameDetail slug={param?.slug} data={resp?.data} />
}

export default page