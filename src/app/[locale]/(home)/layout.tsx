import React from 'react';
import { fetchProfile } from '@/actions/auth';
import Layout from '@/components/layout';
import SessionProvider from '@/components/session/SessionProvider';
import { fetchCategories, fetchChains } from '@/actions/home';

const HomeLayout = async ({ children }: any) => {
  const { data } = await fetchProfile();

  const { data: categories } = await fetchCategories({});
  const { data: chains } = await fetchChains({});

  return (
    <SessionProvider value={{ user: data }}>
      <Layout categories={categories} chains={chains}>{children}</Layout>
    </SessionProvider>
  );
};

export default HomeLayout;
