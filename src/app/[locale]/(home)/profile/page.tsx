import { Metadata } from 'next';
import Profile from '@/components/my-page/components/profile'
import MyPageComponents from '@/components/my-page/container';

const title = "Profile-ClubX"
const description = "Profile-ClubX"

export const metadata: Metadata = {
  title,
  description,
  openGraph: {
    title,
    description,
    type: 'website',
    images: ''
  },
  twitter: {
    title,
    description,
    card: 'summary_large_image',
    images: ''
  }
};

const page = () => {
  return (
    <>
      <MyPageComponents>
        <Profile />
      </MyPageComponents>
    </>
  )
}

export default page