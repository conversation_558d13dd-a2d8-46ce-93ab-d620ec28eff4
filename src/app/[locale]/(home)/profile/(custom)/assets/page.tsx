import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { fetchProfile } from '@/actions/auth';
import { fetchConfiguration } from '@/actions/configuration';
import MyPageComponents from '@/components/my-page/container'
import Assets from '@/components/profile/Container';

const title = "Profile-ClubX"
const description = "Profile-ClubX"

export const metadata: Metadata = {
  title,
  description,
  openGraph: {
    title,
    description,
    type: 'website',
    images: ''
  },
  twitter: {
    title,
    description,
    card: 'summary_large_image',
    images: ''
  }
};

const page = async ({ params }: any) => {
  const { status } = await fetchProfile();
  const { data } = await fetchConfiguration(params);
  // if (status === 'error') {
  //   redirect('/');
  // }

  return <Assets data={data} />
}

export default page