import { Metadata } from 'next';
import ReviewedGames from '@/components/my-page/components/reviewed-games'
import MyPageComponents from '@/components/my-page/container';

const title = "Reviews-ClubX"
const description = "Reviews-ClubX"

export const metadata: Metadata = {
  title,
  description,
  openGraph: {
    title,
    description,
    type: 'website',
    images: ''
  },
  twitter: {
    title,
    description,
    card: 'summary_large_image',
    images: ''
  }
};

const page = () => {
  return (
    <>
      <MyPageComponents>
        <ReviewedGames />
      </MyPageComponents>
    </>
  )
}

export default page