import React from 'react';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { ProgressBarProvider } from '@/providers';
import { ConditionalWalletProvider } from '@/providers/ConditionalWalletProvider';
import { Toaster } from '@/components/ui/toaster';

export default async function LocaleLayout({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body>
        <ConditionalWalletProvider>
          <NextIntlClientProvider messages={messages}>
            <ProgressBarProvider>{children}</ProgressBarProvider>
            <Toaster position="top-right" />
          </NextIntlClientProvider>
        </ConditionalWalletProvider>
      </body>
    </html>
  );
}
