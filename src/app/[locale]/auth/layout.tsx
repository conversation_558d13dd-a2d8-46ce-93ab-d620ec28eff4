import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { fetchCategories, fetchChains } from '@/actions/home';

const HomeLayout = async ({ children }: any) => {

  const { data: categories } = await fetchCategories({});
  const { data: chains } = await fetchChains({});

  return (
    <div>
      <Header />
      <div className='h-full min-h-screen bg-[#09002F] -mt-[1px]'>
        {children}
      </div>
      <Footer categories={categories} chains={chains} />
    </div>
  );
};

export default HomeLayout;
