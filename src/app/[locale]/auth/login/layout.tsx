export default function LoginLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <>
      <script
        dangerouslySetInnerHTML={{
          __html: `
            // Immediate prevention of wallet modals
            (function() {
              // Clear wallet storage
              if (typeof localStorage !== 'undefined') {
                Object.keys(localStorage).forEach(key => {
                  const value = localStorage.getItem(key);
                  if (
                    key.includes('@appkit') || 
                    key.includes('@w3m') || 
                    key.includes('wc@') || 
                    key.includes('wagmi') || 
                    key.includes('reown') || 
                    key.includes('walletconnect') ||
                    key.includes('trust') ||
                    (value && value.includes('trust'))
                  ) {
                    localStorage.removeItem(key);
                  }
                });
              }
              
              // Don't block showModal - let SmartModalBlocker handle it
              
              // Don't hide modals with CSS - let JS handle it intelligently
            })();
          `,
        }}
      />
      {children}
    </>
  )
}