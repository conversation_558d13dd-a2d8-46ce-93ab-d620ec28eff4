/**
 * Wallet configuration settings
 * Centralized configuration for wallet behavior
 */

type StorageType = 'none' | 'session' | 'memory' | 'local';

export const walletConfig = {
  // Storage type: 'none' | 'session' | 'memory' | 'local'
  // 'none' - No persistence, wallet must reconnect every time
  // 'session' - Persists only during browser session
  // 'memory' - In-memory only, lost on page refresh
  // 'local' - Full persistence (default wagmi behavior)
  storageType: 'none' as StorageType,
  
  // Whether to attempt reconnection on mount
  reconnectOnMount: false,
  
  // Debug mode - logs storage operations
  debug: true,
  
  // Routes where AppKit should not be initialized
  disableAppKitRoutes: ['/auth/login'],
  
  // Whether to disable AppKit auto-reconnection globally
  disableAutoConnect: true,
  
  // Wallets to exclude from auto-connect (Trust Wallet often causes issues)
  excludeWalletsFromAutoConnect: ['trust', 'com.trustwallet', '4622a2b2-4caa-4c48-0b6b-4dd1696e8a8b'],
};