import { createAppKit } from '@reown/appkit/react'
import { WagmiAdapter } from '@reown/appkit-adapter-wagmi'
import { base } from 'viem/chains'
import { noopStorage, sessionOnlyStorage, memoryStorage, localStorageWrapper } from './storage'
import { walletConfig } from './wallet-config'

// Your WalletConnect project ID
export const projectId = '4f0beb9c3b7389597a95e176f1f91203'

// Set up metadata
export const metadata = {
  name: 'ClubX',
  description: 'ClubX Gaming Platform',
  url: 'https://clubx.tech',
  icons: ['https://avatars.githubusercontent.com/u/37784886']
}

// Get the configured storage
const getConfiguredStorage = () => {
  switch (walletConfig.storageType) {
    case 'none':
      return noopStorage;
    case 'session':
      return sessionOnlyStorage;
    case 'memory':
      return memoryStorage;
    case 'local':
    default:
      return localStorageWrapper;
  }
};

// Create wagmiAdapter with custom storage based on configuration
export const wagmiAdapter = new WagmiAdapter({
  networks: [base],
  projectId,
  // Pass storage configuration to wagmi's createConfig
  storage: getConfiguredStorage(),
  // Explicitly disable auto-connect features
  ssr: true, // Treat as server-side to prevent auto-connect
})

// Create modal instance
export const appKitInstance = createAppKit({
  adapters: [wagmiAdapter],
  networks: [base],
  metadata,
  projectId,
  features: {
    analytics: true,
    email: false,
    socials: false,
    swaps: false
  },
  themeMode: 'dark',
  themeVariables: {
    '--w3m-font-family': 'Roboto, sans-serif',
    '--w3m-accent': '#7c3aed',
    '--w3m-z-index': 99999,
  },
  // Disable reconnection features
  enableReconnect: false,
  allowUnsupportedChain: false,
  siweConfig: undefined,
} as any);