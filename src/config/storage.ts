import { createStorage } from '@wagmi/core';

/**
 * No-op storage that doesn't persist anything
 * This prevents wagmi from storing connection state
 */
export const noopStorage = createStorage({
  storage: {
    getItem: (key: string) => null,
    setItem: (key: string, value: string) => { },
    removeItem: (key: string) => { },
  },
});

/**
 * Session-only storage that clears on browser close
 */
export const sessionOnlyStorage = createStorage({
  storage: typeof window !== 'undefined' ? window.sessionStorage : undefined,
});

/**
 * Memory storage that only persists during the current page session
 */
export const memoryStorage = (() => {
  const store = new Map<string, string>();
  
  return createStorage({
    storage: {
      getItem: (key: string) => store.get(key) ?? null,
      setItem: (key: string, value: string) => { store.set(key, value); },
      removeItem: (key: string) => { store.delete(key); },
    },
  });
})();

/**
 * Default localStorage (wagmi default behavior)
 */
export const localStorageWrapper = createStorage({
  storage: typeof window !== 'undefined' ? window.localStorage : undefined,
});