"use client"

import { useEffect } from "react"
import { useTranslations } from "next-intl"
import { fetchLogout } from "@/actions/auth"
import { Button } from "@/components/ui/button"
import { useToast } from "@/components/ui/useToast"
import { useDisconnect } from "@reown/appkit/react"
import { Link, usePathname } from "@/hooks/navigation"
import useSession from "@/components/session/useSession"
import { useBreadcrumb } from "@/providers/BreadcrumbProvider"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const MyPageComponents = ({ children }: { children: React.ReactNode; }) => {
  const t = useTranslations()
  const { toast } = useToast();
  const pathname = usePathname();
  const { setSession } = useSession();
  const { disconnect } = useDisconnect();
  const { setBreadcrumb } = useBreadcrumb();

  const handleLogout = async () => {
    try {
      // 1. Disconnect wallet first
      try {
        await disconnect();
      } catch (e) {
        // Ignore wallet disconnect errors
      }

      // 2. Wait a bit to ensure all async operations complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // 3. Logout from backend
      await fetchLogout();

      // 4. Clear session
      setSession({});

      // 5. Show success message
      toast({ variant: 'success', description: 'Logout Success' });

      // 6. Force full page reload to index page
      window.location.replace('/');
    } catch (error) {
      // Force redirect anyway to index page
      window.location.replace('/');
    }
  };

  useEffect(() => {
    setBreadcrumb(
      <Breadcrumb className="flex text-[#6D6D6D] pt-2.5 px-5">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage className="font-bold text-[#6D6D6D]">
              マイページ
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }, [])

  const renderSidebar = () => (
    <div className="lg:w-64 w-full p-4">
      <div className="space-y-2">
        <h2 className="text-lg font-semibold mb-6 text-white">
          {t('my_page.my_page')}
        </h2>

        <nav className="flex lg:flex-col flex-row gap-3 overflow-auto">
          <Link href={'/profile/assets'}>
            <Button
              variant={pathname === "/profile/assets" ? "secondary" : "ghost"}
              className={`w-full h-full py-2 justify-start text-white font-semibold hover:text-white ${pathname === "/profile/assets"
                ? "bg-[#7D69FF]/40 border-2 border-[#7D69FF] hover:bg-[#7D69FF]"
                : "hover:bg-slate-700/50"
                }`}
            >
              {t('my_page.assets')}
            </Button>
          </Link>
          <Link href={'/profile'}>
            <Button
              variant={pathname === "/profile" ? "secondary" : "ghost"}
              className={`w-full h-full py-2 justify-start text-white font-semibold hover:text-white ${pathname === "/profile"
                ? "bg-[#7D69FF]/40 border-2 border-[#7D69FF] hover:bg-[#7D69FF]"
                : "hover:bg-slate-700/50"
                }`}
            >
              {t('my_page.profile')}
            </Button>
          </Link>
          <Link href={'/profile/play-history'}>
            <Button
              variant={pathname === "/profile/play-history" ? "secondary" : "ghost"}
              className={`w-full h-full py-2 justify-start text-white font-semibold hover:text-white ${pathname === "/profile/play-history"
                ? "bg-[#7D69FF]/40 border-2 border-[#7D69FF] hover:bg-[#7D69FF]"
                : "hover:bg-slate-700/50"
                }`}
            >
              {t('my_page.play_history')}
            </Button>
          </Link>
          <Link href={'/profile/favorite'}>
            <Button
              variant={pathname === "/profile/favorite" ? "secondary" : "ghost"}
              className={`w-full h-full py-2 justify-start text-white font-semibold hover:text-white ${pathname === "/profile/favorite"
                ? "bg-[#7D69FF]/40 border-2 border-[#7D69FF] hover:bg-[#7D69FF]"
                : "hover:bg-slate-700/50"
                }`}
            >
              {t('my_page.favorite')}
            </Button>
          </Link>
          <Link href={'/profile/reviews'}>
            <Button
              variant={pathname === "/profile/reviews" ? "secondary" : "ghost"}
              className={`w-full h-full py-2 justify-start text-white font-semibold hover:text-white ${pathname === "/profile/reviews"
                ? "bg-[#7D69FF]/40 border-2 border-[#7D69FF] hover:bg-[#7D69FF]"
                : "hover:bg-slate-700/50"
                }`}
            >
              {t('my_page.reviewed')}
            </Button>
          </Link>
          <div className="lg:pt-8 pt-0">
            <Button
              variant="outline"
              onClick={handleLogout}
              className="w-full h-full py-2 bg-transparent font-semibold border-white hover:bg-[#7D69FF] hover:text-white"
            >
              {t('my_page.logout')}
            </Button>
          </div>
        </nav>
      </div>
    </div>
  )

  return (
    <div className="w-full px-5 bg-[#09002F] text-white">
      <div className="container mx-auto py-10">
        <div className="flex lg:flex-row flex-col">
          {renderSidebar()}
          <div className="flex-1 lg:p-5 p-0">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}

export default MyPageComponents