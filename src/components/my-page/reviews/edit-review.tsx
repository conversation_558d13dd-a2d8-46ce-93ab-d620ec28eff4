'use client'
import React, { useEffect } from 'react'
import ReviewForm from './components/form'
import { useBreadcrumb } from '@/providers/BreadcrumbProvider';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const EditReview = () => {
  const { setBreadcrumb } = useBreadcrumb();
  const existingReviewData = {
    rating: "5",
    nickname: "原ゲーマー",
    comment: "可愛いデザインとわかりやすいゲームシステム。ブロックチェーンゲームが初心者の方でも始めやすく、楽しめるゲームになっています！",
  }

  const handleSubmit = (data: any) => {
    console.log('Updating review:', data)
  }

  const handleDelete = () => {
    console.log('Deleting review')
  }

  useEffect(() => {
    setBreadcrumb(
      <Breadcrumb className="flex text-[#6D6D6D] pt-2.5 px-5">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage className="font-bold text-[#6D6D6D]">
              マイページ
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }, [])

  return (
    <>
      <ReviewForm
        reviewData={existingReviewData}
        onSubmit={() => handleSubmit}
        onDelete={handleDelete}
      />
    </>
  )
}

export default EditReview