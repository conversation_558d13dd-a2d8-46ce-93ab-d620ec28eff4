'use client'
import Image from "next/image"
import { useTranslations } from "next-intl"
import { ThumbsUp, ThumbsDown } from 'lucide-react'
import { Input } from "@/components/ui/Input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import image1 from '@/assets/images/favorite/スクリーンショット-5.png'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface ReviewData {
  rating: string
  nickname: string
  comment: string
}

interface ReviewFormProps {
  reviewData?: ReviewData
  gameTitle?: string
  onSubmit?: () => void
  onDelete?: () => void
}

export default function ReviewForm({
  reviewData,
  gameTitle = "ゲームのタイトル",
  onSubmit,
  onDelete
}: ReviewFormProps) {
  const t = useTranslations('edit_review')
  const isEditMode = !!reviewData

  return (
    <div className="bg-[#09002F] px-5 py-20">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center gap-4">
            <div className="w-20 h-20 rounded-lg overflow-hidden shadow-lg">
              <Image
                src={image1.src || "/placeholder.svg"}
                alt="Game Cover"
                width={80}
                height={80}
                className="w-full h-full object-cover"
              />
            </div>
            <h1 className="text-white text-xl font-medium">{gameTitle}</h1>
          </div>
        </div>

        <div className="bg-[#FFFFFF0D] backdrop-blur-sm rounded-lg p-6 border border-[#1D1A4B]">
          <div className="flex sm:flex-row flex-col items-start justify-between gap-2 mb-6">
            <h2 className="text-white text-lg">
              {isEditMode ? t('edit_your_review') : t('add_review')}
            </h2>
            {isEditMode && (
              <button
                onClick={onDelete}
                className="text-[#E30613] text-sm hover:underline"
              >
                {t('delete_this_review')}
              </button>
            )}
          </div>

          <div className="space-y-4">
            <div className="space-y-1">
              <Label htmlFor="rating" className="text-white text-sm">
                {t('evaluation')}
              </Label>
              <Select defaultValue={reviewData?.rating || ""}>
                <SelectTrigger className="w-full bg-white text-black border-0">
                  <SelectValue placeholder={t('evaluation')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1</SelectItem>
                  <SelectItem value="2">2</SelectItem>
                  <SelectItem value="3">3</SelectItem>
                  <SelectItem value="4">4</SelectItem>
                  <SelectItem value="5">5</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1">
              <Label htmlFor="nickname" className="text-white text-sm">
                {t('nickname')}
              </Label>
              <Input
                id="nickname"
                defaultValue={reviewData?.nickname || ""}
                placeholder={t('nickname')}
                className="bg-white text-black border-0"
              />
            </div>

            <div className="space-y-1">
              <Label htmlFor="comment" className="text-white text-sm">
                {t('comment')}
              </Label>
              <Textarea
                id="comment"
                defaultValue={reviewData?.comment || ""}
                placeholder={t('comment')}
                className="bg-white text-black border-0 min-h-[120px] resize-none"
              />
            </div>

            <div className="space-y-1">
              <Label className="text-white text-sm">
                {t('would_you_recommend_this_game')}
              </Label>
              <div className="flex sm:flex-row flex-col gap-3">
                <Button
                  className="bg-[#6123DB] text-white px-6 py-2 rounded-md flex items-center gap-2 hover:bg-[#401596]"
                >
                  <ThumbsUp className="w-4 h-4" />
                  {t('recommend')}
                </Button>
                <Button
                  className="bg-[#2C146A] text-white border-none px-6 py-2 rounded-md flex items-center gap-2 hover:bg-[#230f57] hover:text-white"
                >
                  <ThumbsDown className="w-4 h-4" />
                  {t('not_recommend')}
                </Button>
              </div>
            </div>

            <div className="pt-3">
              <Button
                onClick={onSubmit}
                className="bg-[#7D69FF] text-white px-8 py-2 rounded-md hover:bg-[#5f4fc7]"
              >
                {isEditMode ? t('update') : t('submit')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
