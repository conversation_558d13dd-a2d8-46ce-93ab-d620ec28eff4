'use client'
import React, { useEffect } from 'react'
import { useBreadcrumb } from '@/providers/BreadcrumbProvider';
import ReviewForm from '@/components/my-page/reviews/components/form'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const ReviewGame = () => {
  const { setBreadcrumb } = useBreadcrumb();
  const handleSubmit = (data: any) => {
    console.log('Adding new review:', data)
  }

  useEffect(() => {
    setBreadcrumb(
      <Breadcrumb className="flex text-[#6D6D6D] pt-2.5 px-5">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage className="font-bold text-[#6D6D6D]">
              マイページ
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }, [])

  return (
    <>
      <ReviewForm
        onSubmit={() => handleSubmit}
      />
    </>
  )
}

export default ReviewGame