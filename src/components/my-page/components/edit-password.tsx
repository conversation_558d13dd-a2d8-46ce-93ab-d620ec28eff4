"use client"

import React, { useState } from "react"
import { useTranslations } from "next-intl"
import { Formik, Form, Field, ErrorMessage } from "formik"
import { editPassword } from "@/validations"
import { Input } from "@/components/ui/Input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"

type Props = {
  onUpdatePassword: (newPassword: string) => void
}

const EditPassword = ({ onUpdatePassword }: Props) => {
  const t = useTranslations()
  const [isSuccess, setIsSuccess] = useState(false)
  return (
    <div>
      <div className="text-white text-lg font-semibold pb-8">
        {t('my_page.change_password')}
      </div>

      {isSuccess && (
        <div className="bg-[#04645C] border border-[#00C888] text-white p-3 rounded font-semibold mb-8 text-center">
          {t('my_page.password_changed')}
        </div>
      )}

      <Formik
        initialValues={{
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        }}
        validationSchema={editPassword(t)}
        onSubmit={(values, { resetForm }) => {
          onUpdatePassword(values.newPassword)
          resetForm()
          setIsSuccess(true)
        }}
      >
        {({ isSubmitting }) => (
          <Form className="w-full max-w-2xl space-y-4">
            <div>
              <Label className="text-white text-sm font-semibold mb-1.5 block">
                {t('my_page.current_password')}
              </Label>
              <Field
                as={Input}
                type="password"
                name="currentPassword"
                placeholder={t('my_page.current_password')}
                className="bg-white text-black border-none h-12 placeholder-gray-500"
              />
              <ErrorMessage name="currentPassword" component="div" className="text-[#E30613] text-sm mt-1" />
            </div>

            <div>
              <Label className="text-white text-sm font-semibold mb-1.5 block">{t('my_page.new_password')}</Label>
              <Field
                as={Input}
                type="password"
                name="newPassword"
                placeholder={t('my_page.new_password')}
                className="bg-white text-black border-none h-12 placeholder-gray-500"
              />
              <ErrorMessage name="newPassword" component="div" className="text-[#E30613] text-sm mt-1" />
            </div>

            <div>
              <Label className="text-white text-sm font-semibold mb-1.5 block">{t('my_page.new_password')}（{t('my_page.comfirmation')}）</Label>
              <Field
                as={Input}
                type="password"
                name="confirmPassword"
                placeholder={`${t('my_page.new_password')}（${t('my_page.comfirmation')}）`}
                className="bg-white text-black border-none h-12 placeholder-gray-500"
              />
              <ErrorMessage name="confirmPassword" component="div" className="text-[#E30613] text-sm mt-1" />
            </div>
            <div className="flex pt-5">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-[#6123DB] hover:bg-[#401596] text-white h-12 font-medium"
              >
                {t('my_page.changePassword')}
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  )
}

export default EditPassword
