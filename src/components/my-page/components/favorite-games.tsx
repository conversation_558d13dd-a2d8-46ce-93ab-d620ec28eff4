"use client"

import { useTranslations } from "next-intl"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import ImageLoader from "@/components/common/ImageLoader"
import image1 from '@/assets/images/favorite/スクリーンショット-1.png'
import image2 from '@/assets/images/favorite/スクリーンショット-2.png'
import image3 from '@/assets/images/favorite/スクリーンショット-3.png'
import image4 from '@/assets/images/favorite/スクリーンショット-4.png'
import image5 from '@/assets/images/favorite/スクリーンショット-5.png'
import image6 from '@/assets/images/favorite/スクリーンショット-6.png'

const FavoriteGames = () => {
  const t = useTranslations()
  const favoriteGames = [
    {
      name: "ゲームのタイトル",
      summay: 'チェーン',
      image: image1.src,
      category: "カテゴリ",
    },
    {
      name: "ゲームのタイトル",
      summay: 'チェーン',
      image: image2.src,
      category: "カテゴリ",
    },
    {
      name: "ゲームのタイトル",
      summay: 'チェーン',
      image: image3.src,
      category: "カテゴリ",
    },
    {
      name: "ゲームのタイトル",
      summay: 'チェーン',
      image: image4.src,
      category: "カテゴリ",
    },
    {
      name: "ゲームのタイトル",
      summay: 'チェーン',
      image: image5.src,
      category: "カテゴリ",
    },
    {
      name: "ゲームのタイトル",
      summay: 'チェーン',
      image: image6.src,
      category: "カテゴリ",
    },
  ]

  return (
    <div className="space-y-6">
      {
        favoriteGames?.length > 0 ? (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6">
              {favoriteGames?.map((game, index) => (
                <Card
                  key={index}
                  className="bg-[#FFFFFF0D] text-[#6D6D6D] border-[#1D1A4B] p-3 overflow-hidden hover:bg-gray-800/70 transition-colors relative"
                >
                  <div className="aspect-video bg-gray-700 rounded-lg overflow-hidden relative">
                    <ImageLoader width={600} height={400} src={game?.image} alt={game?.name} className="w-full h-full object-cover" />
                  </div>
                  <CardContent className="pt-3 px-0 pb-0">
                    <div className="text-white font-semibold mb-2">{game?.name}</div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">{game?.summay}</span>
                      <div className="bg-[#3A3A3A] text-white text-xs px-3 py-1.5 rounded-full">{game?.category}</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="flex justify-center mt-10">
              <Button className="h-full bg-[#7D69FF] text-white font-semibold px-12 py-3 hover:bg-[#5f4fc7]">
                {t('my_page.show_more')}
              </Button>
            </div>
          </>
        ) :
          (
            <Card className="bg-[#150D39] text-white border-none">
              <CardContent className="p-6">
                <div>
                  {t('my_page.no_games_in_your_favorite')}
                </div>
              </CardContent>
            </Card>
          )
      }
    </div>
  )
}

export default FavoriteGames
