"use client"

import Link from "next/link"
import { Star } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useTranslations } from "next-intl"

const ReviewedGames = () => {
  const t = useTranslations()
  const reviews = [
    {
      id: 1,
      name: "ゲームタイトル",
      rating: 5,
      date: "2025.01.15",
      content:
        "2022年に「歩いて稼ぐ」コンセプトで大流行したSTEPNのシステムに触発され、「撮って稼ぐ」という新しいコンセプトを採用したブロックチェーンゲームがこのSNPITです。このゲームでは、カメラNFTを使用して写真を撮り、他のプレイヤーの写真とバトルをして勝つことで報酬を得ることができます。私自身、このゲームに夢中になり、美しい朝日を撮るために海へ行ったり、紅葉を求めて山へ散歩に出かけたりして楽しんでいます。現在はまだ静かな時期ですが、トークンの上場も予定されており、今後の盛り上がりに期待しています。また、無料カメラが提供されるため、無課金ユーザーでも気軽に始められ十分に楽しむことができます。",
      summary: '0人がこのレビューが参考になったと投票しました'
    },
    {
      id: 2,
      name: "ゲームタイトル",
      rating: 4,
      date: "2025.01.10",
      content: "可愛いデザインにわかりやすいゲームシステム。ブロックチェーンゲーム初心者の方でも始めやすく、楽しめるゲームになっています！",
      summary: '0人がこのレビューが参考になったと投票しました'
    },
    {
      id: 3,
      name: "ゲームタイトル",
      rating: 4,
      date: "2025.01.08",
      content: "SNPITをやりはじめてから素敵な場所に多く訪れるようになり、素敵な時間が増えて楽しく遊ばせて頂いてます。 バトルに勝てる写真を探しに行くのも楽しみですね。",
      summary: '0人がこのレビューが参考になったと投票しました'
    },
    {
      id: 4,
      name: "ゲームタイトル",
      rating: 3,
      date: "2025.01.05",
      content: "可愛いデザインにわかりやすいゲームシステム。ブロックチェーンゲーム初心者の方でも始めやすく、楽しめるゲームになっています！",
      summary: '0人がこのレビューが参考になったと投票しました'
    },
    {
      id: 5,
      name: "ゲームタイトル",
      rating: 3,
      date: "2025.01.05",
      content: "SNPITをやりはじめてから素敵な場所に多く訪れるようになり、素敵な時間が増えて楽しく遊ばせて頂いてます。 バトルに勝てる写真を探しに行くのも楽しみですね。",
      summary: '0人がこのレビューが参考になったと投票しました'
    },
  ]

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star key={i} className={`w-4 h-4 ${i < rating ? "fill-[#F4C51E] text-[#F4C51E]" : "text-[#F4C51E]"}`} />
    ))
  }

  return (
    <div className="space-y-6">
      <Card className="bg-[#FFFFFF0D] border-[#1D1A4B] text-white">
        <CardContent className="px-6 py-8">
          {
            reviews?.length > 0 ? (
              <>
                {reviews.map((review, index) => (
                  <div key={index} className={`py-6 border-[#3A3953] border-t ${index === reviews.length - 1 ? 'border-b' : ''}`}>
                    <div className="flex justify-between items-start pb-3">
                      <div className="flex flex-col gap-1">
                        <h3 className="text-white font-semibold">{review.name}</h3>
                        <div className="flex items-center gap-2">
                          <div className="flex">{renderStars(review.rating)}</div>
                        </div>
                      </div>
                      <div className="flex flex-col gap-1 text-right">
                        <Link href={`/profile/reviews/${review?.id}`} prefetch={false}>
                          <Button variant="link" className="text-[#7D69FF] font-semibold hover:text-[#5f4fc7] p-0 h-auto">
                            {t('my_page.edit')}
                          </Button>
                        </Link>
                        <div className="text-[#BCBCBC] text-sm">{review.date}</div>
                      </div>
                    </div>
                    <p className="text-sm leading-relaxed mb-3">{review.content}</p>
                    <p className="text-xs text-[#6D6D6D]">{review.summary}</p>
                  </div>
                ))}
                <div className="flex justify-center mt-8">
                  <Button className="h-full bg-[#7D69FF] text-white font-semibold px-12 py-3 hover:bg-[#5f4fc7]">
                    {t('my_page.show_more')}
                  </Button>
                </div>
              </>
            ) : (
              <div>
                {t('my_page.no_games_reviewed')}
              </div>
            )
          }
        </CardContent>
      </Card>

    </div>
  )
}

export default ReviewedGames
