import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

const Assets = () => {
  return (
    <div className="space-y-8">
      <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700/50">
        <CardHeader>
          <CardTitle className="text-slate-200">ClubXポイント</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-6">
            <div className="text-right ml-auto">
              <span className="text-3xl font-bold text-white">0</span>
              <span className="text-lg text-slate-400 ml-1">P</span>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <Button className="bg-purple-600 hover:bg-purple-700 text-white font-medium">USDCを預ける</Button>
            <Button className="bg-purple-700 hover:bg-purple-800 text-white font-medium">ClubXトークンを預ける</Button>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700/50">
        <CardHeader>
          <CardTitle className="text-slate-200">獲得ポイント</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-6">
            <div className="text-right ml-auto">
              <span className="text-3xl font-bold text-white">0</span>
              <span className="text-lg text-slate-400 ml-1">P</span>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <Button className="bg-purple-600 hover:bg-purple-700 text-white font-medium">ClubXポイントに交換</Button>
            <Button className="bg-purple-700 hover:bg-purple-800 text-white font-medium">引き出す</Button>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700/50">
        <CardHeader>
          <CardTitle className="text-slate-200">ClubX NFT</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <span className="text-slate-400 text-lg">近日公開</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default Assets