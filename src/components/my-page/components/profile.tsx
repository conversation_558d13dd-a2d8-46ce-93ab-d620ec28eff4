'use client'
import Link from 'next/link'
import Image from 'next/image'
import { Copy } from 'lucide-react'
import { useTranslations } from 'next-intl'
import React, { useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/useToast'
import useSession from '@/components/session/useSession'
import { Card, CardContent } from '@/components/ui/card'
import EmailProfile from './email-profile'

const Profile = ({ data }: any) => {
  const { toast } = useToast();
  const { session } = useSession();
  const t = useTranslations();
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [previewUrl, setPreviewUrl] = useState<string>(
    "https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000",
  )

  const handleImageClick = () => {
    fileInputRef.current?.click()
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const onCopy = () => {
    if (!session.code) return;
    navigator.clipboard.writeText(session.code).then(() => {
      toast({ variant: 'success', description: t('copied') });
    });
  };

  const onCopyLink = async () => {
    if (!session.code) return;
    const link = `${window.location.origin}?code=${session.code}`;
    navigator.clipboard.writeText(link).then(() => {
      toast({ variant: 'success', description: t('copied') });
    });
  };

  return (
    <Card className="bg-[#FFFFFF0D] text-[#BCBCBC] backdrop-blur-sm border-[#1D1A4B]">
      <CardContent className="sm:p-8 p-5">
        {
          session?.wallet_address ? (
            <div className="flex sm:flex-row flex-col items-start gap-10">
              <div
                className="w-[100px] h-[100px] rounded-full overflow-hidden bg-slate-300 cursor-pointer"
                onClick={handleImageClick}
                title="クリックして画像を変更"
              >
                <Image width={100} height={100} src={previewUrl} alt="user-image" className="w-full h-full object-cover" />
                <input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept="image/*"
                  onChange={handleImageChange}
                />
              </div>

              <div className="flex-1 space-y-6">
                <div>
                  <h3 className="text-white font-semibold mb-2">
                    {t('my_page.connected_wallet')}
                  </h3>
                  <div className="bg-[#2F2A47] text-white rounded p-3 text-sm break-all">
                    {session?.wallet_address}
                  </div>
                </div>

                <div>
                  <h3 className="text-white font-semibold mb-2">
                    {t('my_page.referral_code')}
                  </h3>
                  <div className="flex sm:flex-row flex-col items-center gap-3">
                    <div className="w-full max-w-sm bg-[#2F2A47] text-white flex items-center justify-between gap-3 rounded px-3 py-1">
                      <div>{session?.code}</div>
                      <Button onClick={onCopy} size="sm" variant="outline" className="bg-transparent border-none">
                        <Copy className="w-4 h-4 mr-1" />
                      </Button>
                    </div>
                    <Button onClick={onCopyLink} size="sm" className="sm:w-auto w-full h-full py-3 bg-[#6123DB] text-white font-medium hover:bg-[#401596]">
                      {t('my_page.copy_share_link')}
                    </Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-white font-semibold mb-2">
                    {t('my_page.email_address_login_information')}
                  </h3>
                  <p className="text-sm mb-3">
                    {t('my_page.no_login_information_with_your_email_address')}
                  </p>
                  <Link href={`/profile/email-register`} prefetch={false}>
                    <Button className="bg-[#6123DB] text-white font-medium hover:bg-[#401596]">
                      {t('my_page.register_with_email_address')}
                    </Button>
                  </Link>
                </div>

                <div>
                  <h3 className="text-white font-semibold mb-2">
                    {t('my_page.withdrawal')}
                  </h3>
                  <p className="text-sm mb-3">
                    {t('my_page.if_you_cancel_your_member_all_history_will_be_deleted')}
                  </p>
                  <Button variant="link" className="text-[#7D69FF] font-semibold hover:text-[#5f4fc7] p-0">
                    {t('my_page.cancel_your_member')}
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <>
              <EmailProfile fileInputRef={fileInputRef} handleImageChange={handleImageChange} handleImageClick={handleImageChange} previewUrl={previewUrl} />
            </>
          )
        }
      </CardContent>
    </Card>
  )
}

export default Profile