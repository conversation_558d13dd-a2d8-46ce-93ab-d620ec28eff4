'use client'
import React, { useState } from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Copy } from 'lucide-react'
import { Input } from '@/components/ui/Input'
import EditEmail from './edit-email'
import EditPassword from './edit-password'
import Link from 'next/link'
import { useTranslations } from 'next-intl'

type Props = {
  handleImageClick: any;
  previewUrl: any;
  fileInputRef: any;
  handleImageChange: any;
}

const EmailProfile = ({ handleImageClick, previewUrl, fileInputRef, handleImageChange }: Props) => {
  const t = useTranslations()
  const [activeForm, setActiveForm] = useState<'email' | 'password' | null>(null)
  const [currentEmail, setCurrentEmail] = useState("<EMAIL>")
  const [currentPassword, setCurrentPassword] = useState('123456789')
  const handleEmailUpdate = (newEmail: string) => {
    setCurrentEmail(newEmail)
    setTimeout(() => {
      setActiveForm(null)
    }, 1000);
  }

  const handlePasswordUpdate = (newPassword: string) => {
    setCurrentPassword(newPassword)
    setTimeout(() => {
      setActiveForm(null)
    }, 1000);
  }

  return (
    <>
      {activeForm === 'email' ? (
        <EditEmail
          currentEmail={currentEmail}
          onUpdateEmail={handleEmailUpdate}
        />
      ) : activeForm === 'password' ? (
        <EditPassword
          onUpdatePassword={handlePasswordUpdate}
        />
      ) :
        (
          <div className="flex sm:flex-row flex-col items-start gap-10">
            <div
              className="w-[100px] h-[100px] rounded-full overflow-hidden bg-slate-300 cursor-pointer"
              onClick={handleImageClick}
              title="クリックして画像を変更"
            >
              <Image width={100} height={100} src={previewUrl} alt="user-image" className="w-full h-full object-cover" />
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                onChange={handleImageChange}
              />
            </div>
            <div className='flex-1 space-y-6'>
              <div className="flex items-center">
                <div>
                  <div className="text-white text-lg font-semibold mb-2">{t('my_page.connected_wallet')}</div>
                  <p className="text-gray-300 text-sm mb-2.5">
                    {t('my_page.no_connected_wallet_please_connect_wallet')}
                  </p>
                  <Button className="bg-[#6123DB] hover:bg-[#401596] text-white text-sm px-4 py-2">
                    {t('my_page.wallet_connection')}
                  </Button>
                </div>
              </div>

              <div>
                <h3 className="text-white font-semibold mb-2">
                  {t('my_page.referral_code')}
                </h3>
                <div className="flex sm:flex-row flex-col items-center gap-3">
                  <div className="w-full max-w-sm bg-[#2F2A47] text-white flex items-center justify-between gap-3 rounded px-3 py-1">
                    <div>4HPRQ</div>
                    <Button size="sm" variant="outline" className="bg-transparent border-none">
                      <Copy className="w-4 h-4 mr-1" />
                    </Button>
                  </div>
                  <Button size="sm" className="sm:w-auto w-full h-full py-3 bg-[#6123DB] text-white font-medium hover:bg-[#401596]">
                    {t('my_page.copy_share_link')}
                  </Button>
                </div>
              </div>

              <div>
                <h3 className="text-white text-lg font-semibold mb-3">{t('my_page.email_address_login_information')}</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-white text-sm font-semibold mb-2">
                      {t('my_page.email_address')}
                    </label>
                    <div className="flex items-center gap-4">
                      <Input
                        type="email"
                        disabled
                        value={currentEmail}
                        className='h-full bg-[#2F2A47] text-white placeholder-white py-2 border-none' />
                      <Button
                        onClick={() => setActiveForm('email')}
                        className="h-full bg-[#6123DB] hover:bg-[#401596] text-white px-6 py-3"
                      >
                        {t('my_page.change')}
                      </Button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-white text-sm font-semibold mb-2">
                      {t('my_page.password')}
                    </label>
                    <div className="flex items-center gap-4">
                      <Input
                        type="password"
                        disabled
                        value={currentPassword}
                        className='h-full bg-[#2F2A47] text-white placeholder-white py-2 border-none' />
                      <Button
                        onClick={() => setActiveForm('password')}
                        className="bg-[#6123DB] hover:bg-[#401596] text-white px-6 py-3">
                        {t('my_page.change')}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-white text-lg font-semibold mb-2">
                  {t('my_page.withdrawal')}
                </h3>
                <p className="text-[#BCBCBC] text-sm mb-3">
                  {t('my_page.if_you_cancel_your_member_all_history_will_be_deleted')}
                </p>
                <Link href={`#`} prefetch={false} className="text-[#7D69FF] font-semibold hover:text-[#5f4fc7] p-0">
                  {t('my_page.cancel_your_member')}
                </Link>
              </div>
            </div>
          </div>
        )
      }
    </>
  )
}

export default EmailProfile