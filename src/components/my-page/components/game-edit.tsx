"use client"

import { useState } from "react"
import { Star, ThumbsUp, ThumbsDown } from "lucide-react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/Input"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function GameReviewForm() {
  const [selectedRating, setSelectedRating] = useState("5")
  const [nickname, setNickname] = useState("")
  const [comment, setComment] = useState(
    "可愛いデザインにわかりやすいゲームシステム。ブロックチェーンゲームが初心者の方でも始めやすく、楽しめるゲームになっています！",
  )
  const [recommendation, setRecommendation] = useState<"recommend" | "not-recommend" | null>("recommend")

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star key={i} className={`w-4 h-4 ${i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-400"}`} />
    ))
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-950 via-purple-950 to-indigo-900 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-4 mb-8 pb-6 border-b border-gray-700">
          <img
            src="/placeholder.svg?height=80&width=80"
            alt="Game thumbnail"
            className="w-20 h-20 rounded object-cover"
          />
          <h1 className="text-white text-xl font-medium">ゲームのタイトル</h1>
        </div>

        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-white text-lg font-medium">レビューを編集する</h2>
            <Button variant="link" className="text-[#E30613] hover:text-red-300 p-0 h-auto">
              このレビューを削除する
            </Button>
          </div>

          <div className="space-y-6">
            <div>
              <Label htmlFor="rating" className="text-white mb-3 block text-sm">
                評価
              </Label>
              <Select value={selectedRating} onValueChange={setSelectedRating}>
                <SelectTrigger className="w-full bg-gray-800 border-gray-600 text-white h-12">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="5" className="text-white">
                    <div className="flex items-center gap-2">
                      <span>5</span>
                      <div className="flex">{renderStars(5)}</div>
                    </div>
                  </SelectItem>
                  <SelectItem value="4" className="text-white">
                    <div className="flex items-center gap-2">
                      <span>4</span>
                      <div className="flex">{renderStars(4)}</div>
                    </div>
                  </SelectItem>
                  <SelectItem value="3" className="text-white">
                    <div className="flex items-center gap-2">
                      <span>3</span>
                      <div className="flex">{renderStars(3)}</div>
                    </div>
                  </SelectItem>
                  <SelectItem value="2" className="text-white">
                    <div className="flex items-center gap-2">
                      <span>2</span>
                      <div className="flex">{renderStars(2)}</div>
                    </div>
                  </SelectItem>
                  <SelectItem value="1" className="text-white">
                    <div className="flex items-center gap-2">
                      <span>1</span>
                      <div className="flex">{renderStars(1)}</div>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="nickname" className="text-white mb-3 block text-sm">
                ニックネーム
              </Label>
              <Input
                id="nickname"
                value={nickname}
                onChange={(e) => setNickname(e.target.value)}
                placeholder="ニックネーム"
                className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 h-12"
              />
            </div>

            <div>
              <Label htmlFor="comment" className="text-white mb-3 block text-sm">
                コメント
              </Label>
              <Textarea
                id="comment"
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 min-h-[120px] resize-none"
              />
            </div>

            <div>
              <p className="text-white mb-4 text-sm">このゲームをオススメしますか？</p>
              <div className="flex gap-4">
                <Button
                  variant={recommendation === "recommend" ? "default" : "outline"}
                  onClick={() => setRecommendation("recommend")}
                  className={`flex items-center gap-2 px-6 py-3 ${recommendation === "recommend"
                    ? "bg-purple-600 hover:bg-purple-700 text-white"
                    : "bg-purple-600/20 border-purple-600 text-purple-400 hover:bg-purple-600/30"
                    }`}
                >
                  <ThumbsUp className="w-4 h-4" />
                  オススメする
                </Button>
                <Button
                  variant={recommendation === "not-recommend" ? "default" : "outline"}
                  onClick={() => setRecommendation("not-recommend")}
                  className={`flex items-center gap-2 px-6 py-3 ${recommendation === "not-recommend"
                    ? "bg-gray-600 hover:bg-gray-700 text-white"
                    : "bg-gray-600/20 border-gray-600 text-gray-400 hover:bg-gray-600/30"
                    }`}
                >
                  <ThumbsDown className="w-4 h-4" />
                  オススメしない
                </Button>
              </div>
            </div>

            <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-4 text-base font-medium">
              更新する
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
