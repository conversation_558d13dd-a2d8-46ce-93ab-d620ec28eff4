'use client'
import React from 'react'
import { FaStar } from "react-icons/fa";
import { FaRegStar } from "react-icons/fa";
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import useSession from '@/components/session/useSession';
import ImageLoader from '@/components/common/ImageLoader';
import ConnectWallet from '@/components/profile/components/connect-wallet';
import image1 from '@/assets/images/play-history/スクリーンショット-2025-05-21-9.55.04.png'
import image2 from '@/assets/images/play-history/スクリーンショット-2025-05-21-9.55.22.png'
import image3 from '@/assets/images/play-history/スクリーンショット-2025-05-21-9.55.38.png'
import image4 from '@/assets/images/play-history/スクリーンショット-2025-05-21-9.55.54.png'
import image5 from '@/assets/images/play-history/スクリーンショット-2025-05-21-9.56.10.png'

const PlayHistory = () => {
  const t = useTranslations()
  const { session } = useSession();
  const data = [
    {
      image: image1.src,
      name: 'ゲームのタイトル',
      subTitle: 'Ethereum',
      category: 'RPG',
      rating: 3
    },
    {
      image: image2.src,
      name: 'ゲームのタイトル',
      subTitle: 'Solana',
      category: 'Play & Earn',
      rating: 5
    },
    {
      image: image3.src,
      name: 'ゲームのタイトル',
      subTitle: 'Ethereum',
      category: 'Move & Earn',
      rating: 4
    },
    {
      image: image4.src,
      name: 'ゲームのタイトル',
      subTitle: 'BNB Chain',
      category: 'eスポーツ',
      rating: 3
    },
    {
      image: image5.src,
      name: 'ゲームのタイトル',
      subTitle: 'Solana',
      category: 'ベッティング',
      rating: 4
    }
  ]

  return (
    <Card className="bg-[#FFFFFF0D] text-white border-none">
      <CardContent className="px-5 py-8">
        {
          session?.wallet_address ? (
            <>
              {
                data?.length > 0 ? (
                  <>
                    {data?.map((item, index) => (
                      <div key={index}>
                        <div className={`py-5 border-[#3A3953] border-t ${index === data.length - 1 ? 'border-b' : ''}`}>
                          <div className="flex md:flex-row flex-col md:items-center gap-6 rounded-lg hover:bg-slate-700/30 transition-colors">
                            <div className="md:w-[160px] w-full md:h-[100px] h-full bg-slate-600 rounded overflow-hidden flex-shrink-0">
                              <ImageLoader
                                width={160}
                                height={100}
                                src={item?.image}
                                alt={item?.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="flex-1">
                              <div className="text-white font-semibold">{item?.name}</div>
                              <div className='text-sm text-[#6D6D6D] pt-1'>{item?.subTitle}</div>
                            </div>
                            <div className='md:w-[40%] w-full flex items-center md:justify-center justify-between gap-5'>
                              <div className="w-2/5 whitespace-nowrap text-white text-center text-xs bg-[#3A3A3A] rounded-full px-5 py-1">{item?.category}</div>
                              <div className="w-3/5 flex flex-col items-center">
                                <div className="text-white text-lg font-bold">{item.rating.toFixed(1)}</div>
                                <div className="flex items-center space-x-1 mb-1">
                                  {Array.from({ length: 5 }, (_, i) => (
                                    <span key={i} className='text-[#F4C51E]'>
                                      {
                                        i < item.rating ? <FaStar /> : <FaRegStar />
                                      }
                                    </span>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                    <div className="pt-14 text-center">
                      <Button className="bg-[#7D69FF] text-white px-8 hover:bg-[#5f4fc7]">
                        {t('my_page.show_more')}
                      </Button>
                    </div>
                  </>
                ) : (
                  <div>
                    {t('my_page.no_games_plaged')}
                  </div>
                )
              }
            </>
          ) : (
            <ConnectWallet title={t('my_page.you_can_check_this_by_connecting_wallet')} />
          )
        }
      </CardContent>
    </Card>
  )
}

export default PlayHistory