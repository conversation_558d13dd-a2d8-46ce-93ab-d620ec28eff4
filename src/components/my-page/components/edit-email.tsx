"use client"

import React, { useState } from "react"
import { useTranslations } from "next-intl"
import { Formik, Form, Field, ErrorMessage } from "formik"
import { Input } from "@/components/ui/Input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { editEmailValidation } from "@/validations"

type Props = {
  currentEmail: string
  onUpdateEmail: (newEmail: string) => void
}

const EditEmail = ({ currentEmail, onUpdateEmail }: Props) => {
  const t = useTranslations()
  const [isSuccess, setIsSuccess] = useState(false)

  return (
    <div>
      <div className="text-white text-lg font-semibold pb-8">
        {t('my_page.update_your_email')}
      </div>

      {isSuccess && (
        <div className="bg-[#04645C] border border-[#00C888] text-white p-3 rounded font-semibold mb-8 text-center">
          {t('my_page.authentication_success_please_login')}
        </div>
      )}

      <Formik
        initialValues={{ newEmail: "" }}
        validationSchema={editEmailValidation(t)}
        onSubmit={(values, { resetForm }) => {
          onUpdateEmail(values.newEmail)
          resetForm()
          setIsSuccess(true)
        }}
      >
        {({ isSubmitting, values, isValid, dirty }) => {
          const showPreSubmitMessage =
            isValid &&
            dirty &&
            values.newEmail !== "" &&
            values.newEmail !== currentEmail

          return (
            <Form className="space-y-6">
              {showPreSubmitMessage && (
                <div className="bg-[#04645C] border border-[#00C888] text-white p-3 rounded font-semibold mb-8 text-center">
                  {t('my_page.your_change_have_been_accepted_please_follow_link')}
                </div>
              )}
              <div className="w-full max-w-2xl space-y-3">
                <div>
                  <Label className="text-white text-sm font-semibold mb-2 block">
                    {t('my_page.your_current_email')}
                  </Label>
                  <Input
                    type="email"
                    value={currentEmail}
                    disabled
                    className="bg-[#2F2A47] text-gray-300 border-none py-2"
                  />
                </div>
                <ErrorMessage
                  name="newEmail"
                  component="div"
                  className="text-[#E30613] text-sm"
                />
                <div>
                  <Label className="text-white text-sm font-semibold mb-2 block">
                    {t('my_page.new_email_address')}
                  </Label>
                  <Field
                    as={Input}
                    type="email"
                    name="newEmail"
                    placeholder={t('my_page.email_address')}
                    className="bg-white text-black border-none py-2 placeholder-gray-500"
                  />
                </div>
                <div className="flex pt-5">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-[#6123DB] hover:bg-[#401596] text-white h-full px-5 py-3 font-medium"
                  >
                    {t('my_page.update_your_email_address')}
                  </Button>
                </div>
              </div>
            </Form>
          )
        }}
      </Formik>
    </div>
  )
}

export default EditEmail
