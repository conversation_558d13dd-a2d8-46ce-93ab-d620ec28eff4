'use client'
import React, { useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { useBreadcrumb } from '@/providers/BreadcrumbProvider'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const Terms = () => {
  const t = useTranslations('terms_policy')
  const { setBreadcrumb } = useBreadcrumb();
  useEffect(() => {
    setBreadcrumb(
      <Breadcrumb className="flex text-[#6D6D6D] pt-2.5">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/" className='uppercase'>Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage className="font-bold text-[#6D6D6D]">
              {t('terms_of_service')}
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }, [])

  return (
    <div className='w-full bg-[#09002F] text-white px-5 py-20'>
      <div className='container mx-auto'>
        <div className='text-3xl font-bold'>
          {t('terms_of_service')}
        </div>
        <div className='flex flex-col gap-5 pt-10'>
          <div>
            利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約
          </div>
          <div>
            テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト
          </div>
          <div>
            利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト
          </div>
          <div>
            利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト
            利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト
            利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト
          </div>
          <div>
            用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト
            利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト
          </div>
          <div>
            利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト利用規約テキスト
          </div>
        </div>
      </div>
    </div>
  )
}

export default Terms