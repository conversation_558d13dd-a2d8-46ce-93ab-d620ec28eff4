import React from 'react'
import { useTranslations } from 'next-intl';

type Props = {
  data: {
    id: number;
    title: string;
    description: string;
  }[]
}

const Steps = ({ data }: Props) => {
  const t = useTranslations()
  return (
    <div >
      <h2 className="text-2xl md:text-3xl font-semibold text-center mb-12 text-[#7D69FF]">
        {t('abouts.start_your_clubx')}
      </h2>
      <div className="space-y-6">
        {
          data?.map((item, index) => (
            <div key={index} className="flex sm:flex-row flex-col items-center gap-10 p-6 rounded-2xl bg-[#FFFFFF0D] backdrop-blur-sm border border-[#1D1A4B]">
              <div className="w-[70px] h-[94px] flex flex-col items-center justify-center gap-3 shrink-0">
                <div className="w-[70px] h-[70px] bg-[#6123DB] rounded-full flex flex-col items-center justify-center text-sm font-bold flex-shrink-0">
                  <div className="uppercase">Step</div>
                  <div className='text-lg'>{item?.id}</div>
                </div>
                <div className="flex flex-col gap-1">
                  <div className="w-1 h-1 bg-[#6123DB] rounded-full"></div>
                  <div className="w-1 h-1 bg-[#6123DB] rounded-full"></div>
                  <div className="w-1 h-1 bg-[#6123DB] rounded-full"></div>
                </div>
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-4">{item?.title}</h3>
                <p>
                  {item?.description}
                </p>
              </div>
            </div>
          ))
        }

      </div>
    </div>
  )
}

export default Steps