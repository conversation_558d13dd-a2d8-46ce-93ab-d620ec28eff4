import React from 'react'
import ImageLoader from '@/components/common/ImageLoader'

type Props = {
  data: {
    image: string;
    title: string;
    description: string;
  }
}

const WhyChoose = ({ data }: Props) => {
  return (
    <div className="flex flex-col gap-5 text-white text-center">
      <div className="w-[130px] h-[130px] bg-[#6123DBB2] border-8 border-[#230B63] rounded-full flex items-center justify-center mx-auto overflow-hidden shrink-0">
        <div className="w-[60px] h-[60px]">
          <ImageLoader width={60} height={60} src={data?.image} alt={data?.title} className='w-full h-full' />
        </div>
      </div>
      <h3 className="text-xl font-semibold">{data?.title}</h3>
      <p className="leading-relaxed">
        {data?.description}
      </p>
    </div>
  )
}

export default WhyChoose