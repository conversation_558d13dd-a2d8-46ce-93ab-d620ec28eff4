'use client'
import { useEffect } from 'react'
import { useTranslations } from 'next-intl'
import nft from '@/assets/images/abouts/nft.svg'
import { useBreadcrumb } from '@/providers/BreadcrumbProvider'
import playEarnImage from '@/assets/images/abouts/play-earn.svg'
import biginnerImage from '@/assets/images/abouts/biginner-friendly.svg'
import bottomBg from '@/assets/images/abouts/ed087ff725e10dad8027ab04932be4bd9bd08969.png'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Steps from "./components/steps"
import HeroTitle from "./components/page-title"
import WhyChoose from "./components/why-choose"

const Abouts = () => {
  const { setBreadcrumb } = useBreadcrumb();
  const t = useTranslations()
  const chooseUs = [
    {
      image: playEarnImage.src,
      title: t('abouts.strong_community'),
      description: t('abouts.innovation_platform_that_arrow_you_to_earn_money')
    },
    {
      image: nft.src,
      title: t('abouts.utilizing_nfts'),
      description: t('abouts.our_unique_nft_and_token')
    },
    {
      image: biginnerImage.src,
      title: t('about.safe_enviroment'),
      description: t('abouts.provide_the_highest_level')
    }
  ]

  const stepData = [
    {
      id: 1,
      title: t('about.game_participation'),
      description: t('about.choose_your_favorite_game')
    },
    {
      id: 2,
      title: t('abouts.connect_your_wallet'),
      description: t('abouts.support_metamask')
    },
    {
      id: 3,
      title: t('abouts.play_games'),
      description: t('abouts.earn_nfts_and_token')
    }
  ]

  useEffect(() => {
    setBreadcrumb(
      <Breadcrumb className="flex text-[#6D6D6D] p-2.5">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/" className='uppercase'>Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage className="font-bold text-[#6D6D6D]">
              {t('abouts.what_is_clubx')}
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }, [])

  return (
    <div className="bg-[#09002F] text-white relative overflow-hidden ">
      <HeroTitle />
      <div className="container mx-auto px-5">
        <div className="relative z-10 max-w-7xl mx-auto">
          <div className="flex flex-col md:gap-y-32 gap-y-20">
            <div className="text-center pt-5">
              <h2 className="text-2xl md:text-3xl font-semibold mb-10 text-[#7D69FF]">{t('abouts.what_is_clubx')}？</h2>
              <div className="max-w-4xl mx-auto">
                <p className="leading-relaxed text-gray-300 mb-6">
                  {t('abouts.about_clubx')}
                </p>
              </div>
            </div>
            <div>
              <h2 className="text-2xl md:text-3xl font-semibold text-center mb-12 text-[#7D69FF]">
                {t('abouts.why_choose_clubx')}
              </h2>
              <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-10">
                {
                  chooseUs?.map((item, index) => (
                    <WhyChoose key={index} data={item} />
                  ))
                }
              </div>
            </div>
            <Steps data={stepData} />
          </div>
        </div>
      </div>
      <div className="md:pt-32 pt-20">
        <div className="w-full h-[400px] bg-cover bg-center" style={{ backgroundImage: `url(${bottomBg.src})` }}>
          <div className="w-full h-full flex items-center justify-center px-5">
            <div className='container mx-auto'>
              <div className=" max-w-7xl mx-auto flex flex-col items-end">
                <div>
                  <h2 className="text-2xl md:text-3xl font-semibold mb-[30px] text-[#7D69FF]">
                    {t('abouts.the_future_of_web3')}
                  </h2>
                  <div className="text-white leading-relaxed">
                    {t('abouts.new_area_gaming_begins')}
                    <br />
                    {t('abouts.join_us_and_experience')}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Abouts