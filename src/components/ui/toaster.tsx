"use client";

import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast";
import { useToast } from "@/components/ui/useToast";
import { cn } from "@/lib/utils";

const showAt: any = {
  "top-left": "sm:top-0 sm:left-0",
  "top-right": "sm:top-0 sm:right-0",
  "bottom-left": "sm:bottom-0 sm:left-0",
  "bottom-right": "sm:bottom-0 sm:right-0",
};

const slideBy: any = {
  "top-left":
    "data-[state=open]:sm:slide-in-from-top-full data-[state=closed]:slide-out-to-left-full mb-1 !pointer-events-auto",
  "top-right":
    "data-[state=open]:sm:slide-in-from-top-full data-[state=closed]:slide-out-to-right-full mb-1 !pointer-events-auto",
  "bottom-left":
    "data-[state=open]:sm:slide-in-from-bottom-full data-[state=closed]:slide-out-to-left-full mb-1 !pointer-events-auto",
  "bottom-right":
    "data-[state=open]:sm:slide-in-from-bottom-full data-[state=closed]:slide-out-to-right-full mb-1 !pointer-events-auto",
};

type Props = {
  position?: "top-left" | "top-right" | "bottom-left" | "bottom-right";
};

export function Toaster({ position, ...props }: Props) {
  const { toasts } = useToast();

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, ...props }: any) {
        return (
          <Toast
            key={id}
            {...props}
            className={cn(
              props?.className,
              position ? slideBy[position] : slideBy["bottom-right"],
            )}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && (
                <ToastDescription>{description}</ToastDescription>
              )}
            </div>
            {action}
            <ToastClose />
          </Toast>
        );
      })}
      <ToastViewport
        className={position ? showAt[position] : showAt["bottom-right"]}
      />
    </ToastProvider>
  );
}
