import * as React from 'react';

import {cn} from '@/lib/utils';
import {FieldProps} from 'formik';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  field?: FieldProps['field'];
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(({className, type, ...props}, ref) => {
  return (
    <input
      type={type}
      className={cn(
        'border border-black py-3 w-full px-2 rounded-sm bg-[#eaeaea] text-black text-xl focus:outline-none',
        className,
      )}
      ref={ref}
      {...(props?.field || {})}
      {...props}
    />
  );
});
Input.displayName = 'Input';

export {Input};
