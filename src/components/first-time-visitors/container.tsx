'use client'
import React, { useEffect } from 'react'
import { useTranslations } from 'next-intl';
import { Button } from "@/components/ui/button";
import stepImage1 from '@/assets/images/first-time/Frame.svg';
import { useBreadcrumb } from '@/providers/BreadcrumbProvider';
import stepImage2 from '@/assets/images/first-time/Frame-1.svg';
import stepImage3 from '@/assets/images/first-time/Frame-2.svg';
import bgTitle from '@/assets/images/first-time/69fbeaea68980d50217efe6157e164ed4af6890b.png'
import bottomBg from '@/assets/images/first-time/9f958e01c0403245c1b727c85436b4ca14af3472.png'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Steps from './components/steps';
import Terms from './components/terms';
import FAQ from './components/faq';

const FirstTimeVisitors = () => {
  const { setBreadcrumb } = useBreadcrumb();
  const t = useTranslations()

  const steps = [
    {
      image: stepImage1.src,
      title: t('first_time_visitors.prepare_your_wallet'),
      description: t('first_time_visitors.install_a_cryptocurrency_wallet')
    },
    {
      image: stepImage2.src,
      title: t('first_time_visitors.connect_your_wallet_to_clubx'),
      description: t('first_time_visitors.click_login_button_on_top_screen')
    },
    {
      image: stepImage3.src,
      title: t('first_time_visitors.select_and_start_game'),
      description: t('first_time_visitors.choose_your_favorite_game')
    }
  ]

  const terms = [
    {
      title: t('first_time_visitors.blockchain'),
      description: t('first_time_visitors.tamper_proof_decentralized_system')
    },
    {
      title: 'Web3',
      description: t('first_time_visitors.user_driven_next_generation_internet')
    },
    {
      title: 'NFT',
      description: t('first_time_visitors.unique_digital_item')
    },
    {
      title: t('first_time_visitors.gas_bill'),
      description: t('first_time_visitors.fees_for_blockchain')
    },
    {
      title: t('first_time_visitors.wallet'),
      description: t('first_time_visitors.digital_wallet_for_storing')
    },
    {
      title: 'Play & Earn',
      description: t('first_time_visitors.games_style_where_you_can_earn')
    }
  ]

  const faq = [
    {
      id: 1,
      title: t('first_time_visitors.what_do_i_need_to_play_at_clubx'),
      description: t('first_time_visitors.all_you_need_is_a_wallet')
    },
    {
      id: 2,
      title: t('first_time_visitors.can_i_play_without_buying_an_nft'),
      description: t('first_time_visitors.all_you_need_is_a_wallet')
    },
    {
      id: 3,
      title: t('first_time_visitors.can_i_use_it_on_a_smartphone'),
      description: t('first_time_visitors.all_you_need_is_a_wallet')
    }
  ]

  useEffect(() => {
    setBreadcrumb(
      <Breadcrumb className="flex text-[#6D6D6D] p-2.5">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/" className='uppercase'>Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage className="font-bold text-[#6D6D6D]">
              {t("first_time_visitors.first_time_visitors")}
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }, [])

  return (
    <div className="bg-[#09002F] text-white relative overflow-hidden">
      <div className='w-full flex justify-center bg-[#09002F] bg-cover bg-center' style={{ backgroundImage: `url(${bgTitle.src})` }}>
        <div className='text-3xl font-bold px-5 py-36'>
          {t('first_time_visitors.for_new_to_clubx')}
        </div>
      </div>
      <div className='container mx-auto px-5 pt-20'>
        <div className='w-full max-w-7xl mx-auto flex flex-col md:gap-y-32 gap-y-20'>
          <Steps data={steps} />
          <Terms data={terms} />
          <FAQ data={faq} />
        </div>
      </div>
      <section className="bg-[#09002F] bg-cover bg-center md:mt-32 mt-20" style={{ backgroundImage: `url(${bottomBg.src})` }}>
        <div className="container mx-auto px-5 py-20">
          <div className='w-full max-w-7xl mx-auto flex sm:flex-row flex-col items-center justify-between gap-10'>
            <div>
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
                {t('first_time_visitors.solid_support_system')}
              </h2>
              <p className="text-purple-100 mb-8">
                {t('first_time_visitors.comprehensive_support_system')}
              </p>
            </div>
            <Button className="h-full bg-[#7D69FF] text-white px-12 py-3 rounded-[5px] font-bold hover:bg-[#5f4fc7]">
              {t('first_time_visitors.inquiry')}
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}

export default FirstTimeVisitors