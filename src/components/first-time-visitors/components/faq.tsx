import React from 'react'
import { useTranslations } from 'next-intl';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'

type Props = {
  data: {
    id: number;
    title: string;
    description: string;
  }[]
}

const FAQ = ({ data }: Props) => {
  const t = useTranslations('first_time_visitors')
  return (
    <section >
      <h2 className="text-2xl md:text-3xl font-bold text-white text-center mb-12">
        {t('faq')}
      </h2>
      <Accordion type="single" collapsible className="space-y-[30px]">
        {
          data?.map((item, index) => (
            <AccordionItem key={index} value={`item-${index}`} className="bg-[#FFFFFF0D] rounded-lg border border-[#1D1A4B] ">
              <AccordionTrigger className="flex items-center justify-between w-full p-4 text-left hover:no-underline">
                <div className="flex items-center gap-5">
                  <div className="w-10 h-10 bg-[#7D69FF] rounded-full flex items-center justify-center text-white text-lg font-bold shrink-0">
                    {item?.id}
                  </div>
                  <span className="text-white sm:text-lg text-base font-semibold">{item?.title}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4 text-white text-sm">
                <hr className="w-full h-px mt-0 mb-4 bg-[#3A3953] border-0"></hr>
                {item?.description}
              </AccordionContent>
            </AccordionItem>
          ))
        }
      </Accordion>
    </section>
  )
}

export default FAQ