import React from 'react'
import { useTranslations } from 'next-intl';

type Props = {
  data: {
    title: string;
    description: string;
  }[]
}

const Terms = ({ data }: Props) => {
  const t = useTranslations('first_time_visitors')
  return (
    <section>
      <h2 className="text-2xl md:text-3xl font-bold text-white text-center mb-12">
        {t('explanation_of_common_terms')}
      </h2>
      <div className="grid md:grid-cols-2 grid-cols-1 gap-5">
        {
          data?.map((item, index) => (
            <div key={index} className='bg-[#FFFFFF0D] rounded-[10px] border border-[#1D1A4B] p-5 flex flex-col gap-[10px]'>
              <div className="text-[#7D69FF] font-bold">{item?.title}</div>
              <p className="text-sm leading-relaxed">
                {item?.description}
              </p>
            </div>
          ))
        }
      </div>
    </section>
  )
}

export default Terms