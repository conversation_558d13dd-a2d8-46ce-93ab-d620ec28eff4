import React from 'react'
import { useTranslations } from 'next-intl';
import ImageLoader from '@/components/common/ImageLoader'

type Props = {
  data: {
    image: string;
    title: string;
    description: string;
  }[]
}

const Steps = ({ data }: Props) => {
  const t = useTranslations('first_time_visitors')
  return (
    <section>
      <h1 className="text-2xl md:text-3xl font-bold text-white text-center mb-12">
        {t('start_play_web_game')}
      </h1>
      <div className="grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-10">
        {
          data?.map((item, index) => (
            <div key={index}>
              <div className="text-[#7D69FF] text-sm font-fond">STEP</div>
              <div className="text-4xl font-bold text-[#7D69FF] mb-6">{index}</div>
              <div className="relative h-32">
                <div className="absolute inset-0  flex items-center justify-center">
                  <ImageLoader width={400} height={300} src={item?.image} alt='' className='w-full h-full' />
                </div>
              </div>
              <div className='bg-[#FFFFFF0D] border border-[#1D1A4B] text-white px-5 pt-20 pb-[30px] -mt-12 rounded-[10px] flex flex-col gap-5'>
                <h3 className="font-bold">{item?.title}</h3>
                <p className="text-sm leading-relaxed">
                  {item?.description}
                </p>
              </div>
            </div>
          ))
        }
      </div>
    </section>
  )
}

export default Steps