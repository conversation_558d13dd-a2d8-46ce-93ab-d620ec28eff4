'use client'
import React, { useEffect, useState, useTransition } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import PageCard from '@/components/common/page-card'
import { fetchEvents, fetchLatestGames } from '@/actions/home'
import { useBreadcrumb } from '@/providers/BreadcrumbProvider'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Loader2 } from 'lucide-react'

const EventsPage = ({ type, data, meta }: any) => {
  const t = useTranslations()
  const [games, setGames] = useState(data)
  const [paging, setPaging] = useState(meta)
  const { setBreadcrumb } = useBreadcrumb();
  const [isPending, startTransition] = useTransition()
  const handleShowMore = async () => {
    startTransition(async () => {
      let newData: [], newMeta;
      let nextPage = Number(paging?.currentPage + 2);
      let resp;
      if (type === 'latest') {
        resp = await fetchLatestGames({ page: nextPage })
      } else {
        resp = await fetchEvents({ page: nextPage })
      }
      newData = resp?.data
      newMeta = resp?.meta
      setGames((prev: any) => [...prev, ...newData])
      setPaging(newMeta)
    })
  }

  useEffect(() => {
    setBreadcrumb(
      <Breadcrumb className="flex text-[#6D6D6D] pt-2.5">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/" className='uppercase'>Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage className="font-bold text-[#6D6D6D]">
              {type === 'latest' ? t('events_page.new_and_upcoming') : t('events_page.events_campaigns')}
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }, [])

  return (
    <>
      <div className='flex flex-col gap-4'>
        <header className="flex items-center justify-between">
          <div className="text-2xl md:text-3xl font-bold">
            {type === 'latest' ? t('events_page.new_and_upcoming') : t('events_page.events_campaigns')}
          </div>
        </header>
        {
          games?.length > 0 ? (
            <div>
              <div className='grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-5'>
                {
                  games?.map((item: any, index: any) => (
                    <PageCard key={index} data={item} />
                  ))
                }
              </div>
              {
                paging?.currentPage + 2 <= paging?.totalPage && (
                  <div className="pt-14 text-center">
                    <Button
                      type='button'
                      disabled={isPending}
                      className="bg-[#7D69FF] text-white font-semibold px-10 hover:bg-[#5f4fc7]" onClick={handleShowMore}>
                      {
                        isPending ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          t('play_earn_page.show_more')
                        )
                      }
                    </Button>
                  </div>
                )
              }
            </div>
          ) : (
            <div className='text-center text-3xl py-20'>
              {t('events_page.no_event_post_yet')}
            </div>
          )
        }
      </div>
    </>
  )
}

export default EventsPage