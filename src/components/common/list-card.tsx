import React from 'react'
import { Star } from 'lucide-react';
import ImageLoader from './ImageLoader';

type Props = {
  data: any;
  index: number;
}

const ListCard = ({ data, index }: Props) => {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star key={i} className={`w-4 h-4 ${i < rating ? "fill-[#F4C51E] text-[#F4C51E]" : "text-[#F4C51E]"}`} />
    ))
  }

  return (
    <div className='w-full min-w-3xl flex items-center gap-5 p-3 rounded-lg hover:bg-white/10 transition-colors cursor-pointer'>
      <div className="flex-shrink-0">
        <ImageLoader
          width={170}
          height={100}
          src={data?.image}
          alt={data?.name?.en}
          className="w-[170px] h-[100px] rounded object-cover bg-[#3A3A3A]"
        />
      </div>
      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-[#7D69FF] flex items-center justify-center text-sm font-bold">
        {index}
      </div>
      <div className="flex-1 min-w-0">
        <h3 className="font-semibold text-white truncate">{data?.name?.en}</h3>
        <p className="text-sm text-[#6D6D6D]">{data?.chain?.name}</p>
      </div>

      <div className='w-2/5 flex'>
        <div className="w-2/5 flex-shrink-0">
          <div className="bg-[#3A3A3A] text-white text-center text-xs px-2 py-1 rounded-full">
            {data?.category?.name}
          </div>
        </div>
        <div className="w-full flex flex-col items-center">
          <div className="text-sm font-bold text-white">{4}</div>
          <div className="flex">{renderStars(4)}</div>
        </div>
      </div>
    </div>
  )
}

export default ListCard