import React from 'react'
import { Star, ThumbsDown, ThumbsUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ImageLoader from './ImageLoader'

type Props = {
  data: any;
}

const ReviewCard = ({ data }: Props) => {
  return (
    <div key={data?.id} className="text-white">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full overflow-hidden shrink-0">
            <ImageLoader width={40} height={40} src={data?.image} alt={data?.username} className='w-full h-full object-cover' />
          </div>
          <div>
            <p className="font-medium text-[#BCBCBC]">{data?.username}</p>
            <div className="flex items-center space-x-1 mt-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`w-4 h-4 ${star <= data?.rating ? "fill-[#F4C51E] text-[#F4C51E]" : "text-[#F4C51E]"
                    }`}
                />
              ))}
            </div>
          </div>
        </div>
        <span className="text-[#BCBCBC] text-sm">{data?.date}</span>
      </div>
      <div className="mb-4">
        <p className="leading-relaxed whitespace-pre-line">{data?.content}</p>
      </div>
      <div className="flex sm:flex-row flex-col items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          className="sm:w-auto w-full h-full py-2 bg-transparent border-white text-white hover:bg-white/20 hover:text-white flex items-center space-x-2"
        >
          <ThumbsUp className="w-4 h-4" />
          <span>参考になった</span>
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="sm:w-auto w-full h-full py-2 bg-transparent border-white text-white hover:bg-white/20 hover:text-white flex items-center space-x-2"
        >
          <ThumbsDown className="w-4 h-4" />
          <span>参考にならなかった</span>
        </Button>
        <span className="text-[#BCBCBC] text-sm ml-auto">
          {data?.helpfulCount}人がこのレビューが参考になったと投稿しました
        </span>
      </div>
    </div>
  )
}

export default ReviewCard