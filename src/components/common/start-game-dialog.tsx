'use client';
import React, { ForwardedRef, forwardRef, useImperativeHandle, useState, useTransition } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { Formik, Form } from 'formik';
import { isEmpty } from 'lodash';
import { TfiClose } from "react-icons/tfi";
import { handleStartGame } from '@/actions/games';
import useSession from '@/components/session/useSession';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/useToast';
import LoginForm from '@/components/auth/login/LoginForm';

export interface PropertyFrom {
  open: (params?: any) => void;
  close: () => void;
}

export interface PropertyFromState { }

interface ComponentProps { }

const StartGameDialog = forwardRef(({ }: ComponentProps, ref: ForwardedRef<PropertyFrom>) => {
  const t = useTranslations('profile');
  const { session } = useSession();
  const router = useRouter();
  const [formData, setFormData] = useState<{
    isOpen: boolean;
    data: any;
  }>({ isOpen: false, data: {} });
  const { toast } = useToast();
  const [isPending, startTransition] = useTransition();
  const { isOpen, data } = formData;
  // const [isLogin, setIsLogin] = useState(false)

  const open = (params?: any) => {
    setFormData({ isOpen: true, data: params });
  };

  const close = () => {
    setFormData(prev => ({ data: prev.data, isOpen: false }));
  };

  useImperativeHandle(ref, () => ({ open, close }), []);

  const onSubmit = async () => {
    if (isEmpty(session)) {
      close()
      router.push('/auth/login')
    } else {
      startTransition(async () => {
        const { status, url } = await handleStartGame(data.id);
        if (status === 'error') {
          toast({
            variant: 'error',
            description: t('startGameError'),
          });
          close();
        } else {
          close();
          setTimeout(() => {
            window.open(url, '_blank');
          }, 300);
        }
      });
    }
  };
  return (
    <div>
      <Dialog open={isOpen}>
        <DialogContent className="bg-transparent border-0 justify-center ">
          <div className="w-[350px] border-white border bg-black text-white shadow-[-10px_-10px_100px_20px_rgba(29,97 35 219 ),_10px_10px_100px_20px_rgba(29,97 35 219 )] py-16 relative">
            <TfiClose onClick={close} size={20} className='top-[-30px] absolute right-0 cursor-pointer' />
            <div className="text-center text-[28px] font-bold">{t('startGame')}</div>
            <Formik initialValues={{ amount: 0 }} onSubmit={onSubmit}>
              {_ => (
                <Form>
                  <div className="flex justify-center">
                    <Button
                      disabled={isPending}
                      type="submit"
                      className="w-[210px] flex text-2xl font-bold justify-center text-white py-8 bg-[#6123DB] mt-3 rounded-md ">
                      {t('start')}
                    </Button>
                  </div>
                  <div className="flex justify-center">
                    <Button
                      disabled={isPending}
                      type="button"
                      variant="outline"
                      className="w-[180px] text-xl font-bold flex justify-center text-[#606972] py-7 gap-2 mt-16 rounded-md shadow-none bg-[#1f1f1f] border border-[#606972]"
                      onClick={close}>
                      <TfiClose size={16} />
                      {t('cancel')}
                    </Button>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </DialogContent>
      </Dialog>
      {/* {
        isLogin && <LoginForm onClose={() => setIsLogin(false)} />
      } */}
    </div>
  );
});

StartGameDialog.displayName = 'StartGameDialog';

export default StartGameDialog;