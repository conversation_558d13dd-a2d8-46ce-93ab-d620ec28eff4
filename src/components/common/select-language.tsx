"use client";
import clsx from "clsx";
import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { BiSolidDownArrow } from "react-icons/bi";
import { usePathname, useRouter } from "@/hooks/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const SelectLanguage = () => {
  const [selectLanguage, setSelectLanguage] = useState({
    value: "us",
    label: "🇺🇸",
  });
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale();

  const languageOption = [
    { value: "us", label: "🇺🇸" },
    { value: "jp", label: "🇯🇵" },
  ];

  useEffect(() => {
    const currentLanguage = languageOption.find(
      (lang) => lang.value === locale
    );
    if (currentLanguage) {
      setSelectLanguage(currentLanguage);
    }
  }, [locale]);

  const handleLanguageChange = (locale: any) => {
    router.replace(pathname, { locale });
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <DropdownMenu onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild className="border border-white px-3 py-1 rounded-full">
          <div className="flex items-center gap-3 text-xl cursor-pointer">
            {selectLanguage?.label}
            <div className="text-sm font-medium uppercase">{selectLanguage.value}</div>
            <BiSolidDownArrow
              size={12}
              className={clsx(
                "transition-transform duration-200",
                isOpen && "rotate-180"
              )}
            />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-10 absolute -left-16 z-[1002] bg-white dark:bg-[#131A28]">
          <DropdownMenuGroup>
            {languageOption.map((item, index: any) => (
              <div key={item.value}>
                <DropdownMenuItem
                  onClick={() => handleLanguageChange(item.value)}
                  className={clsx(
                    "flex justify-center gap-3 cursor-pointer ",
                    item.value === locale
                      ? "bg-gray-100 dark:bg-gray-700"
                      : "focus:bg-gray-100 dark:focus:bg-gray-700"
                  )}
                >
                  <p className="text-xl">{item.label}</p>
                  <div className="uppercase">{item?.value}</div>
                </DropdownMenuItem>
                {index !== languageOption.length - 1 && (
                  <DropdownMenuSeparator />
                )}
              </div>
            ))}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default SelectLanguage;
