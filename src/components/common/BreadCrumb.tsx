"use client";
import React from "react";
import {
  Bread<PERSON>rumb,
  BreadcrumbI<PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

export function BreadCrumb({ replaceLinks = [] }: any) {
  const pathName = usePathname();
  const getPathSegments = () => {
    return pathName.split("/").filter(Boolean);
  };

  const getReplacedSegment = (index: number) => {
    const replaceLink = replaceLinks.find((rl: any) => rl.index === index);
    return replaceLink;
  };

  return (
    <Breadcrumb className="flex text-[#6D6D6D] pt-2.5 px-5">
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink href="/" className="uppercase">Home</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        {getPathSegments().map((segment, index, segments) => {
          const replacedLink = getReplacedSegment(index);
          return (
            <React.Fragment key={segment}>
              <BreadcrumbItem>
                {index !== segments.length - 1 ? (
                  <BreadcrumbLink
                    className={cn(!replacedLink?.title && "text-[#6D6D6D] capitalize")}
                    href={replacedLink?.link || `/${segment}`}>
                    {replacedLink?.title || segment}
                  </BreadcrumbLink>
                ) : (
                  <BreadcrumbPage
                    className={cn(
                      !replacedLink?.title && "text-[#6D6D6D] capitalize",
                      segments.join("/") === pathName ? "font-bold" : "",
                    )}>
                    {replacedLink?.title || segment}
                  </BreadcrumbPage>
                )}
              </BreadcrumbItem>
              {index !== segments.length - 1 && <BreadcrumbSeparator />}
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
