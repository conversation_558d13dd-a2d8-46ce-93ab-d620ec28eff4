import React from 'react'
import { <PERSON> } from '@/hooks/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import ImageLoader from './ImageLoader'

type Props = {
  data: any;
  slug?: any;
}

const PageCard = ({ data, slug }: Props) => {
  return (
    <Link href={`/games/${data?.id}`} prefetch={false} className='cursor-pointer'>
      <Card className="bg-[#FFFFFF0D] border-[#1D1A4B] overflow-hidden group transition-all duration-300 hover:bg-black/40">
        <CardContent className="p-3">
          <div
            className={`relative w-full h-auto aspect-video rounded-lg flex items-center justify-center overflow-hidden`}
          >
            <ImageLoader
              src={data?.image}
              alt={data?.name?.en}
              width={300}
              height={200}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black/10" />
          </div>
          <div className="space-y-2 pt-3">
            <h3 className="text-white font-semibold text-base">{data?.name?.en}</h3>
            {
              data?.description && (
                <p className="text-[#BCBCBC] text-sm leading-relaxed line-clamp-2">{data?.short_description}</p>
              )
            }
            <div className="flex justify-between gap-2 pt-1">
              <div
                className="text-xs text-[#6D6D6D]"
              >
                {data?.chain?.name}
              </div>
              {
                data?.category && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-transparent border-white text-white text-xs px-3 py-1.5 rounded-full h-auto"
                  >
                    {data?.category?.name}
                  </Button>
                )
              }
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}

export default PageCard