import React from 'react'
import { MdCircle } from "react-icons/md";

const DepositPopup = ({ onClose, title, fund, description, deposit }: {
  onClose: () => void;
  title: string;
  fund: string;
  description: string;
  deposit: string;
}) => {
  const handleOverlayClick = (e: any) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };
  return (
    <div onClick={handleOverlayClick} className="overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 flex justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full bg-black/50">
      <div className="relative p-4 w-full max-w-2xl max-h-full">
        <div className="relative bg-white shadow p-5">
          <div className="flex items-center justify-end p-4 md:p-5 rounded-t">
            <button onClick={onClose} type="button" className="text-black bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="default-modal">
              <svg className="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
              </svg>
              <span className="sr-only">Close modal</span>
            </button>
          </div>
          <div className='text-center'>
            <div className='text-3xl'>{title}</div>
            <div className='flex items-end gap-5 mt-7'>
              <input className="border border-black text-gray-900 text-sm focus:ring-none focus:border-none block w-full p-2.5 " required />
              <div>{fund}</div>
            </div>
            <div className='flex items-center justify-center mt-3 gap-1'>
              <MdCircle className='text-xs' />
              <div className='text-sm'>{description}</div>
            </div>
            <div className='w-full flex justify-center sm:text-lg text-base py-3 bg-[#D9D9D9] mt-3 cursor-pointer'>{deposit}</div>
            <div onClick={onClose} className='w-full flex justify-center sm:text-lg text-base py-3 mt-3 cursor-pointer'>Cancel</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DepositPopup