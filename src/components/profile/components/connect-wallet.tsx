'use client'
import React from 'react'
import { useTranslations } from 'next-intl';
import { useAppKit } from '@reown/appkit/react';
import { Button } from '@/components/ui/button';

type Props = {
  title: string;
}

const ConnectWallet = ({ title }: Props) => {
  const t = useTranslations()
  const { open } = useAppKit();
  const handleConnect = () => {
    open();
  };

  return (
    <div>
      <div>
        <div>{title}</div>
      </div>
      <div className='flex pt-5'>
        <Button
          className="text-center bg-[#7D69FF] text-white font-semibold cursor-pointer py-2 hover:bg-[#5f4fc7]"
          onClick={handleConnect}>
          {t('my_page.wallet_connection')}
        </Button>
      </div>
    </div>
  )
}

export default ConnectWallet