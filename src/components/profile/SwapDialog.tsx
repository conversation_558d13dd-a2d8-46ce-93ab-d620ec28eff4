'use client';
import React, { ForwardedRef, forwardRef, useImperativeHandle, useState, useTransition } from 'react';
import { useTranslations } from 'next-intl';
import { Formik, Form, Field } from 'formik';
import { keyBy } from 'lodash';
import { TfiClose } from 'react-icons/tfi';
import { DepositValidation } from '@/validations';
import { handleSwap } from '@/actions/wallet';
import useSession from '@/components/session/useSession';
import LoadingIcon from '@/components/icons/Loading';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/useToast';
import BgBlue from '@/assets/images/bg-blue.webp';

export interface PropertyFrom {
  open: (params?: any) => void;
  close: () => void;
}

export interface PropertyFromState { }

interface ComponentProps { }

const SwapDialog = forwardRef(({ }: ComponentProps, ref: ForwardedRef<PropertyFrom>) => {
  const t = useTranslations('profile');
  const { session, setSession } = useSession();
  const [formData, setFormData] = useState<{
    isOpen: boolean;
    data: any;
  }>({ isOpen: false, data: {} });
  const { toast } = useToast();
  const [isPending, startTransition] = useTransition();
  const { isOpen } = formData;

  const open = (params?: any) => {
    setFormData({ isOpen: true, data: params });
  };

  const close = () => {
    setFormData(prev => ({ data: prev.data, isOpen: false }));
  };

  useImperativeHandle(ref, () => ({ open, close }), []);
  const wallets = keyBy(session.wallets, 'type');

  const formattedEarnPointBalance = Intl.NumberFormat('en', { currency: 'USD' }).format(wallets?.earned_point?.balance || 0);

  const onSubmit = async (values: any, { setErrors }: any) => {
    startTransition(async () => {
      if (Number(values?.amount) > wallets?.earned_point?.balance) {
        return;
      }
      const payload = await handleSwap({
        amount: values.amount,
        currency: 'ctx',
      });
      if (payload.status === 'error') {
        toast({
          variant: 'error',
          description: payload.message,
        });
      } else {
        toast({
          variant: 'success',
          description: payload.data.message,
        });
        setSession((prev: any) => ({
          ...prev,
          wallets: prev.wallets.map((wallet: any) => {
            if (wallet.type === 'earned_point') {
              return {
                ...wallet,
                balance: wallet.balance - Number(values?.amount),
              };
            }
            if (wallet.type === 'clubx_point') {
              return {
                ...wallet,
                balance: wallet.balance + Number(values?.amount),
              };
            }
            return wallet;
          }),
        }));
        close();
      }
    });
  };
  return (
    <Dialog open={isOpen}>
      <DialogContent className="bg-transparent border-0 justify-center ">
        <div className="w-[350px] border-white border bg-black text-white shadow-[-10px_-10px_100px_20px_rgba(29,108,121,255),_10px_10px_100px_20px_rgba(29,108,121,255)] py-16 relative px-8">
          <TfiClose onClick={close} size={20} className="top-[-30px] absolute right-0 cursor-pointer" />
          <div className="text-center text-[28px] font-bold pb-4">{t('swapClubXPoint')}</div>
          <div className="relative z-10">
            <img src={BgBlue.src} className="absolute z-1 top-[-24%] " />
            <div className="relative z-10 bg-black pt-4">
              <Formik initialValues={{ amount: '' }} onSubmit={onSubmit} validationSchema={DepositValidation(t)} validateOnBlur={false} >
                {formik => (
                  <Form>
                    <div className="mt-5 flex items-center justify-center">
                      <Field
                        placeholder="0,000,000"
                        className="border border-black py-4 w-full px-2 rounded-sm text-end font-bold bg-[#eaeaea] text-[#606972] text-3xl focus:outline-none"
                        name="amount"
                        onInput={(e: any) => {
                          const value = e.target.value
                            .replace(/[^0-9.]/g, '')
                            .replace(/(\..*)\..*/g, '$1')
                            .replace(/^(\d*\.?\d{0,2}).*/, '$1');
                          e.target.value = value.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                        }}
                      />
                      <div className="ml-2 font-bold">P</div>
                    </div>
                    <div>
                      {formik.errors.amount && <div className="text-red-500 text-sm">{formik.errors.amount}</div>}
                    </div>
                    <div className="text-center text-[#606972] font-bold mt-3 mr-5">{`${t('max')}  ${formattedEarnPointBalance} P`}</div>
                    <div className="flex justify-center w-full">
                      <Button
                        disabled={isPending}
                        type="submit"
                        className="w-full flex justify-center text-2xl text-black font-bold py-8 mt-5 rounded-sm bg-gradient-to-r from-[#6beff2] to-[#02B8D7]">
                        {isPending ? <LoadingIcon className="w-6 h-6 animate-spin" /> : t('swap')}
                      </Button>
                    </div>
                    <div className="flex justify-center w-full mt-16">
                      <Button
                        disabled={isPending}
                        type="button"
                        variant="outline"
                        className="w-7/12 flex justify-center text-xl font-bold text-[#606972] border border-[#606972] py-6 mt-3 bg-[#1f1f1f] rounded-sm gap-2"
                        onClick={close}>
                        <TfiClose size={20} />
                        {t('cancel')}
                      </Button>
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

SwapDialog.displayName = 'SwapDialog';

export default SwapDialog;
