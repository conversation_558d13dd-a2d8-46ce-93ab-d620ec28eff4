'use client';
import React, { ForwardedRef, forwardRef, useImperativeHandle, useState, useTransition } from 'react';
import { useTranslations } from 'next-intl';
import { Formik, Form, Field } from 'formik';
import { BrowserProvider, Contract, ethers } from 'ethers';
import { useAppKitProvider } from '@reown/appkit/react';
import abi from '@/abi/usdc.abi.json';
import purchaseAbi from '@/abi/deposit.abi.json';
import { TfiClose } from 'react-icons/tfi';
import { setupNetwork } from '@/lib/setupNetwork';
import { CHAIN_ID } from '@/constant';
import { handleTopUp } from '@/actions/wallet';
import { DepositValidation } from '@/validations';
import LoadingIcon from '@/components/icons/Loading';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/useToast';
import BgBlue from '@/assets/images/bg-blue.webp';

export interface PropertyFrom {
  open: (params?: any) => void;
  close: () => void;
}

export interface PropertyFromState { }

interface ComponentProps {
  rateExchange: any
}

const DepositDialog = forwardRef(({ rateExchange }: ComponentProps, ref: ForwardedRef<PropertyFrom>,) => {
  const t = useTranslations('profile');
  const [formData, setFormData] = useState<{
    isOpen: boolean;
    data: any;
  }>({ isOpen: false, data: {} });
  const { toast } = useToast();
  const [isPending, startTransition] = useTransition();
  const { isOpen, data } = formData;
  const { walletProvider } = useAppKitProvider('eip155');

  const open = (params?: any) => {
    setFormData({ isOpen: true, data: params });
  };

  const close = () => {
    setFormData(prev => ({ data: prev.data, isOpen: false }));
  };

  useImperativeHandle(ref, () => ({ open, close }), []);

  const onSubmit = async (values: any, { setErrors }: any) => {
    startTransition(async () => {
      try {
        if (Number(values.amount) <= 0) {
          return;
        }
        const payload = await handleTopUp({
          amount: values.amount,
          currency: data.type === 'usdc' ? 'usdc' : 'cxt',
        });
        console.log({payload})
        const provider = new BrowserProvider(walletProvider as any);
        await setupNetwork(provider, process.env.MODE === 'production' ? CHAIN_ID.base : CHAIN_ID.baseSepolia);
        const signer = await provider.getSigner();
        const usdtContract = new Contract(process.env.CLUBX_USDT_ADDRESS || '', abi, signer);
        const purchaseContract = new Contract(process.env.CLUBX_PURCHASE_ADDRESS || '', purchaseAbi, signer);
        const address = await signer.getAddress();
        const allowance = await usdtContract.allowance(address, process.env.CLUBX_PURCHASE_ADDRESS);
        console.log({allowance, address, CLUBX_PURCHASE_ADDRESS: process.env.CLUBX_PURCHASE_ADDRESS})
        const formatAmount = ethers.formatUnits(payload.data.data.convertAmount, 6);
        const formatAllowance = ethers.formatUnits(allowance, 6);
        if (parseFloat(formatAllowance) < parseFloat(formatAmount)) {
          const approve = await usdtContract.approve(
            process.env.CLUBX_PURCHASE_ADDRESS,
            payload.data.data.convertAmount,
          );
          await approve.wait();
        }

        const deposit = await purchaseContract.deposit(
          payload.data?.data?.convertAmount,
          // payload.data?.data?.deadline,
        );
        await deposit.wait();
        toast({
          variant: 'success',
          description: 'Deposit success',
        });
        close();
      } catch (error: any) {
        console.log('error', error);
        toast({
          variant: 'error',
          description: error.reason || 'Deposit failed',
        });
        close();
      }
    });
  };

  return (
    <div>
      <Dialog open={isOpen}>
        <DialogContent className="bg-transparent border-0 justify-center">
          <div className="w-[350px] md:w-[420px] border-white border bg-black text-white shadow-[-10px_-10px_100px_20px_rgba(29,108,121,255),_10px_10px_100px_20px_rgba(29,108,121,255)] py-16 relative sm:px-8 px-5">
            <TfiClose onClick={close} size={20} className="top-[-30px] absolute right-0 cursor-pointer" type="button" />
            <div className="text-center text-[28px] font-bold pb-4">
              {data.type === 'usdc' ? t('depositUSDC') : t('depositClubxToken')}
            </div>
            <div className="relative z-10">
              <img src={BgBlue.src} className="absolute z-1 top-[-24%] " />
              <div className="relative z-10 bg-black pt-4">
                <Formik
                  initialValues={{ amount: '' }}
                  onSubmit={onSubmit}
                  validationSchema={DepositValidation(t)}
                  validateOnBlur={false}>
                  {formik => (
                    <Form>
                      <div className="mt-5 flex items-center justify-center">
                        <Field
                          placeholder="0,000,000"
                          className="border border-black py-4 w-full px-2 rounded-sm text-end font-bold bg-[#eaeaea] text-[#606972] text-3xl focus:outline-none"
                          name="amount"
                          onInput={(e: any) => {
                            const value = e.target.value
                              .replace(/[^0-9.]/g, '')
                              .replace(/(\..*)\..*/g, '$1')
                              .replace(/^(\d*\.?\d{0,2}).*/, '$1');
                            e.target.value = value.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                          }}
                        />
                        <div className="ml-2 font-bold">{data.type === 'usdc' ? 'USDC' : 'CTX'}</div>
                      </div>
                      <div>
                        {formik.errors.amount && <div className="text-red-500 text-sm">{formik.errors.amount}</div>}
                      </div>
                      <div className="text-center text-[#606972] font-bold mt-3">
                        {data.type === 'usdc' ? `1 USDC = 1 ClubX Point` : `${rateExchange?.usdtExchangeRate} ClubX Token = ${rateExchange?.cxtExchangeRate} ClubX Point`}
                      </div>
                      <div className="flex justify-center w-full">
                        <Button
                          disabled={isPending}
                          type="submit"
                          className="w-full flex justify-center text-2xl text-black font-bold py-8 mt-5 rounded-sm bg-gradient-to-r from-[#6beff2] to-[#02B8D7]">
                          {isPending ? <LoadingIcon className="w-6 h-6 animate-spin" /> : t('deposit')}
                        </Button>
                      </div>
                      <div className="flex justify-center w-full mt-16">
                        <Button
                          disabled={isPending}
                          type="button"
                          variant="outline"
                          className="w-7/12 flex justify-center text-xl font-bold text-[#606972] border border-[#606972] py-6 mt-3 bg-[#1f1f1f] rounded-sm gap-2"
                          onClick={close}>
                          <TfiClose size={20} />
                          {t('cancel')}
                        </Button>
                      </div>
                    </Form>
                  )}
                </Formik>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
});

DepositDialog.displayName = 'DepositDialog';

export default DepositDialog;
