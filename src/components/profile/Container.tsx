'use client';
import keyBy from 'lodash/keyBy';
import React, { useRef } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import useSession from '@/components/session/useSession';
import SwapDialog from './SwapDialog';
import DepositDialog from './DepositDialog';
import ConnectWallet from './components/connect-wallet';

const Assets = ({ data }: any) => {
  const t = useTranslations();
  const { session } = useSession();
  const wallets = keyBy(session.wallets, 'type');
  const depositDialogRef = useRef<any>(null);
  const swapDialogRef = useRef<any>(null);

  const formattedClubXPoint = Intl.NumberFormat('en', { currency: 'USD' }).format(wallets?.clubx_point?.balance || 0);
  const formattedEarnPoint = Intl.NumberFormat('en', { currency: 'USD' }).format(wallets?.earned_point?.balance || 0);

  const handleDeposit = (params: any) => {
    depositDialogRef.current.open(params);
  };
  const handleSwapOpen = () => {
    swapDialogRef.current.open();
  };

  return (
    <div>
      <div className="w-full container mx-auto lg:px-12 relative z-10">
        <div className="relative text-white bg-[#FFFFFF0D] border border-[#1D1A4B] rounded-lg px-5 py-8 flex flex-col justify-between gap-10">
          {
            session?.wallet_address ? (
              <>
                <div className="flex-1">
                  <div className="flex items-center">
                    <div className="flex-1 font-bold text-xl">{t('profile.clubXPoint')}</div>
                  </div>
                  <div className="text-center pt-1">
                    <div className="bg-[#2F2A47] px-3 py-4 rounded-sm">
                      <div className="sm:text-3xl text-xl font-bold text-end">
                        {formattedClubXPoint || 0}
                        <span className="text-lg ml-2">P</span>
                      </div>
                    </div>
                    <div className='flex sm:flex-row flex-col items-center gap-2 mt-2'>
                      <Button
                        className="w-full flex justify-center text-lg font-bold text-white py-6 bg-[#7D69FF] rounded-sm hover:bg-[#5f4fc7]"
                        onClick={() => handleDeposit({ type: 'usdc' })}>
                        {t('profile.depositUSDC')}
                      </Button>
                      <Button
                        className="w-full flex justify-center text-lg font-bold text-white py-6 bg-[#6123DB4D] rounded-sm"
                        disabled>
                        {t('profile.despositClubXToken')}
                      </Button>
                    </div>
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center">
                    <div className="flex-1 font-bold text-xl">{t('profile.earnedPoint')}</div>
                  </div>
                  <div className="text-center pt-1">
                    <div className="bg-[#2F2A47] px-3 py-4 rounded-sm">
                      <div className="sm:text-3xl text-xl font-bold text-end">
                        {formattedEarnPoint || 0}
                        <span className="text-lg ml-2">P</span>{' '}
                      </div>
                    </div>
                    <div className='flex sm:flex-row flex-col items-center gap-2 mt-2'>
                      <Button
                        className="w-full flex justify-center text-lg font-bold text-white py-6 bg-[#7D69FF] rounded-sm hover:bg-[#5f4fc7]"
                        onClick={handleSwapOpen}>
                        {t('profile.swapClubXPoint')}
                      </Button>
                      <Button className="w-full flex justify-center text-lg font-bold text-white py-6 bg-[#6123DB] rounded-sm hover:bg-[#401596]">
                        {t('profile.withdraw')}
                      </Button>
                    </div>
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center">
                    <div className="flex-1 font-bold text-xl">{t('profile.clubXNFT')}</div>
                  </div>
                  <div className="w-full bg-[#2F2A474D] border border-[#2F2A47] border-dashed flex justify-center items-center text-xl py-5 mt-1 rounded-lg">
                    {t('profile.comingSoon')}...
                  </div>
                </div>
              </>
            ) : (
              <ConnectWallet title={t('my_page.connect_your_wallet_you_will_see_your_assets')} />
            )
          }
        </div>
      </div>
      <div className="px-5">
        <DepositDialog ref={depositDialogRef} rateExchange={data} />
        <SwapDialog ref={swapDialogRef} />
      </div>
    </div>
  );
};

export default Assets;
