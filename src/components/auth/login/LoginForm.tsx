'use client';
import {useEffect, useMemo, useState, useTransition} from 'react';
import {useTranslations} from 'next-intl';
import {usePathname, useRouter} from 'next/navigation';
import CryptoJS from 'crypto-js';
import {<PERSON><PERSON><PERSON><PERSON>rovider} from 'ethers';
import { useAppKit, useAppKitEvents, useAppKitAccount, useAppKitProvider, useDisconnect } from '@reown/appkit/react';
import {TfiClose} from 'react-icons/tfi';
import {decryptKey} from '@/lib/encryptKey';
import * as AuthAction from '@/actions/auth';
import {handleStartGame} from '@/actions/games';
import useSession from '@/components/session/useSession';
import {useToast} from '@/components/ui/useToast';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/Input';
import {ErrorMessage, Field, Form, Formik} from 'formik';
import {verifyCodeValidation} from '@/validations';
import {cn} from '@/lib/utils';

interface Props {
  callback?: string;
  onClose?: any;
  paramsCode?: string | null;
}

const LoginForm = ({callback, onClose, paramsCode}: Props) => {
  const { open } = useAppKit();
  const { data: eventsData } = useAppKitEvents();
  const { isConnected, address } = useAppKitAccount();
  const router = useRouter();
  const t = useTranslations('');
  const { walletProvider } = useAppKitProvider('eip155');
  const { disconnect } = useDisconnect();
  const { toast } = useToast();
  const { setSession } = useSession();
  const [code, setCode] = useState({value: '', isApplied: false});
  const [isVerifying, setVerifyTransition] = useTransition();
  const pathname = usePathname();

  const initialValues = useMemo(() => ({code: code.value}), [code.value]);

  const handleConnect = () => {
    open();
  };

  const handleWeb3Login = async () => {
    if (address) {
      try {
        const nounParams = {address} as any;
        if (code.value && code.isApplied) nounParams.code = code.value;
        const {data, status: nounStatus, message: nounMessage} = await AuthAction.fetchNounce(nounParams);
        if (nounStatus !== 'success') {
          toast({
            variant: 'error',
            description: nounMessage,
          });
          disconnect();
          return;
        }
        const nounce = data?.nounce;
        const provider = new BrowserProvider(walletProvider as any);
        const signer = await provider.getSigner();
        const preMessage = `Welcome to Club X!\n\nClick \"Connect\" to integrate your wallet with your account.\n\nI accept the Club X Terms of Service: https://clubx.tech/terms\n\nWallet address:\n${address}\nNounce:\n${nounce}`;
        const reference = CryptoJS.MD5(`${preMessage}_${nounce}-${address?.toLowerCase()}`).toString();
        const signatureMessage = `Welcome to Club X!\n\nClick \"Connect\" to integrate your wallet with your account.\n\nI accept the Club X Terms of Service: https://clubx.tech/terms\n\nWallet address:\n${address}\nNounce:\n${nounce}\nReference\n${reference}`;

        try {
          const signature = await signer.signMessage(signatureMessage);
          const {status, data: resp, message} = await AuthAction.fetchWallets(address, signature);
          if (status === 'success') {
            if (callback) {
              const id = decryptKey(callback);
              if (id) {
                const {status, url} = await handleStartGame(id);
                if (status !== 'success') {
                  toast({
                    variant: 'error',
                    description: t('startGameError'),
                  });
                  return;
                }
                toast({
                  variant: 'success',
                  description: resp.message,
                });
                router.push('/');
                window.open(url, '_blank');
                return;
              }
            }
            toast({
              variant: 'success',
              description: resp.message,
            });
            onClose();
            router.replace(pathname);
            setSession(resp.user);
            return;
          }
          toast({
            variant: 'error',
            description: message,
          });
          disconnect();
        } catch(err) {
          console.error('Signature error:', err);
        }
      } catch (error) {
        console.error('Login error:', error);
        disconnect();
      }
    }
  };

  const onVerifyCode = (values: any, actions?: any) => {
    if (!values?.code) return;
    setVerifyTransition(async () => {
      const referralCode = values?.code?.trim()?.toUpperCase();
      const {status, errors, data} = await AuthAction.verifyCode({code: referralCode});
      if (status !== 'success') {
        actions
          ? actions.setErrors(errors)
          : toast({
              variant: 'error',
              description: errors?.code,
            });
        return;
      }
      setCode({value: referralCode, isApplied: true});
      toast({
        variant: 'success',
        description: data.message,
      });
    });
  };

  const handleChangeButton = (formik: any) => {
    setCode({value: '', isApplied: false});
    formik.setFieldValue('code', '');
  };

  useEffect(() => {
    if (paramsCode && !code.isApplied) {
      onVerifyCode({code: paramsCode});
    }
  }, []);

  // Track if we've already attempted login to prevent infinite loops
  const [hasAttemptedLogin, setHasAttemptedLogin] = useState(false);

  // Auto-login when wallet connects, but track to prevent loops
  useEffect(() => {
    // Only proceed if we have an address and are connected
    if (address && isConnected && !hasAttemptedLogin) {
      handleWeb3Login();
      setHasAttemptedLogin(true);
    }
    
    // Reset the flag when disconnected
    if (!isConnected) {
      setHasAttemptedLogin(false);
    }
  }, [isConnected, address]);
  
  // Handle modal events separately
  useEffect(() => {
    if (eventsData && eventsData.event === 'CONNECT_SUCCESS') {
      // The connection was successful, login will be handled by the above effect
      console.log('Connection successful');
    }
  }, [eventsData]);
  console.log({address, isConnected, hasAttemptedLogin})
  return (
    <div className="fixed top-0 left-0 flex justify-center items-center w-full h-screen z-50 bg-black/80">
      <div className="relative p-4 w-full max-w-2xl max-h-full overflow-auto">
        <div className="w-full flex justify-end">
          <Button onClick={onClose} variant="none" className="text-white cursor-pointer p-2">
            <TfiClose size={20} />
          </Button>
        </div>
        <div className="relative bg-black text-white border border-white shadow h-[500px] mt-2">
          <div className="w-full h-full flex flex-col justify-center items-center p-5">
            <h1 className="sm:text-4xl text-3xl mb-5 font-bold">ClubX</h1>
            <hr className="bg-[#02B8D7] w-full h-0.5" />
            <h1 className="text-xl mt-12">{t('login.connectWallet')}</h1>
            <span className="text-center text-accent/80 my-2">{t('login.code_desc')}</span>
            <div className="grid w-full gap-2">
              <Formik
                validateOnBlur={false}
                validateOnChange={false}
                enableReinitialize
                initialValues={initialValues}
                validationSchema={verifyCodeValidation(t)}
                onSubmit={onVerifyCode}>
                {formik => {
                  return (
                    <Form className="grid space-y-0.5 w-full">
                      <div className="flex gap-2">
                        <Field
                          name="code"
                          className={cn(
                            'w-full text-left disabled:opacity-50 uppercase',
                            code.isApplied && 'border-2 border-[#02B8D7]',
                          )}
                          component={Input}
                          disabled={isVerifying || code.isApplied}
                          placeholder={t('profile.referral_code')}
                          autoComplete="off"
                        />
                        {code.isApplied ? (
                          <button
                            type="button"
                            className="disabled:opacity-50 text-center flex gap-2 sm:w-24 w-28  shrink-0 items-center justify-center bg-gradient-to-r from-[#6beff2] to-[#02B8D7] text-black text-lg cursor-pointer px-4 rounded-sm"
                            onClick={() => handleChangeButton(formik)}>
                            {t('commons.remove')}
                          </button>
                        ) : (
                          <button
                            type="submit"
                            disabled={!formik.dirty}
                            className="disabled:opacity-50 w-28 shrink-0 text-center flex gap-2 items-center justify-center bg-gradient-to-r from-[#6beff2] to-[#02B8D7] text-black text-lg cursor-pointer px-4 rounded-sm">
                            {isVerifying ? t('commons.checking') : t('commons.check')}
                          </button>
                        )}
                      </div>
                      <span className="text-sm text-destructive">
                        <ErrorMessage name="code" />
                      </span>
                    </Form>
                  );
                }}
              </Formik>
              <div
                className="w-full text-center bg-gradient-to-r from-[#6beff2] to-[#02B8D7] text-black text-lg cursor-pointer py-2 rounded-sm"
                onClick={handleConnect}>
                {t('login.loginWithWallet')}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;