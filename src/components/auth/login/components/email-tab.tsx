import React from 'react'
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/button';
import { Link } from '@/hooks/navigation';
import { toast } from '@/components/ui/useToast';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

const EmailTab = () => {
  const router = useRouter()
  const t = useTranslations()

  const handleSubmit = async (values: { email: string; password: string }) => {
    const { email, password } = values;

    if (email === '<EMAIL>' && password === '123456789') {
      toast({ variant: 'success', description: 'ログイン成功！' })

      // Save login info (mock)
      localStorage.setItem('isEmailLoggedIn', 'true');
      localStorage.setItem('email', email);

      router.push('/');
    } else {
      toast({ variant: 'error', description: 'メールアドレスまたはパスワードが間違っています' })
    }
  };


  return (
    <div className="space-y-4 mt-4">
      <Formik
        initialValues={{ email: '', password: '' }}
        // validationSchema={LoginSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting }) => (
          <Form className="w-full space-y-4">
            <div>
              <label className="text-sm font-medium">
                {t('login.email_address')}
              </label>
              <Field
                name="email"
                type="email"
                placeholder={t('login.email_address')}
                component={Input}
                className='w-full text-left !text-sm'
              />
              <ErrorMessage name="email" component="div" className="text-red-500 text-sm" />
            </div>

            <div>
              <label className="text-sm font-medium">
                {t('login.password')}
              </label>
              <Field
                name="password"
                type="password"
                placeholder={t('login.password')}
                component={Input}
                className='w-full text-left !text-sm'
              />
              <ErrorMessage name="password" component="div" className="text-red-500 text-sm" />
            </div>
            <div className='py-3'>
              <Button
                type="submit"
                className="w-full h-full bg-[#7D69FF] hover:bg-[#5f4fc7] text-white font-semibold py-3 text-lg rounded-sm"
                disabled={isSubmitting}
              >
                {isSubmitting ? `${t('login.logged_in')}...` : `${t('login.login')}`}
              </Button>
            </div>

            <div className="text-center">
              <button type="button" className="text-sm text-[#7D69FF] font-semibold hover:underline">
                {t('login.forget_your_password')}
              </button>
            </div>
            <hr className="h-px my-8 bg-[#3A3953] border-0" />
            <div className="text-center">
              <Link href='/auth/register' prefetch={false} className="text-sm text-[#7D69FF] font-semibold hover:underline">
                {t("login.don't_have_account")}
              </Link>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  )
}

export default EmailTab