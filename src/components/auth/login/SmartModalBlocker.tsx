'use client'

import { useEffect, useRef } from 'react'
import { usePathname } from 'next/navigation'

export function SmartModalBlocker() {
  const pathname = usePathname()
  const userClickedRef = useRef(false)
  const lastUserClickTime = useRef(0)
  const hasBlockedAutoOpen = useRef(false)
  
  useEffect(() => {
    if (!pathname?.includes('/auth/login')) return
    
    console.log('[SmartModalBlocker] Active - blocking auto-open only');
    
    // Track user clicks on wallet buttons with better detection
    const handleClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement
      
      // Check if clicked element or any parent is a wallet-related button
      let element: HTMLElement | null = target
      while (element) {
        const text = element.textContent?.toLowerCase() || ''
        const className = element.className?.toLowerCase() || ''
        const id = element.id?.toLowerCase() || ''
        
        if (
          element.tagName === 'BUTTON' ||
          text.includes('wallet') ||
          text.includes('connect') ||
          text.includes('trust') ||
          text.includes('metamask') ||
          className.includes('wallet') ||
          id.includes('wallet')
        ) {
          console.log('[SmartModalBlocker] User clicked wallet-related element');
          userClickedRef.current = true
          lastUserClickTime.current = Date.now()
          
          // Keep user click state active for 5 seconds
          setTimeout(() => {
            userClickedRef.current = false
          }, 5000)
          
          break
        }
        
        element = element.parentElement
      }
    }
    
    // Capture clicks early in the event phase
    document.addEventListener('click', handleClick, true)
    document.addEventListener('mousedown', handleClick, true)
    
    // Only check for auto-open in the first 5 seconds after page load
    const pageLoadTime = Date.now()
    
    const checkForAutoOpen = () => {
      const timeSincePageLoad = Date.now() - pageLoadTime
      const timeSinceUserClick = Date.now() - lastUserClickTime.current
      
      // Only block if:
      // 1. User hasn't clicked recently (within 5 seconds)
      // 2. We're within the first 5 seconds of page load
      // 3. We haven't already blocked an auto-open
      if (
        !userClickedRef.current && 
        timeSincePageLoad < 5000 && 
        !hasBlockedAutoOpen.current &&
        timeSinceUserClick > 1000
      ) {
        const modalOpen = localStorage.getItem('@appkit/modal_open')
        
        if (modalOpen === 'true') {
          console.log('[SmartModalBlocker] Detected auto-open, blocking...');
          localStorage.setItem('@appkit/modal_open', 'false')
          localStorage.removeItem('@appkit/active_view')
          hasBlockedAutoOpen.current = true
          
          // Remove modal from DOM after a short delay
          setTimeout(() => {
            const modals = document.querySelectorAll('w3m-modal, appkit-modal, dialog[open]')
            modals.forEach(modal => {
              console.log('[SmartModalBlocker] Removing auto-opened modal from DOM');
              modal.remove()
            })
          }, 100)
        }
      }
    }
    
    // Only monitor for the first 5 seconds
    const interval = setInterval(checkForAutoOpen, 50)
    
    setTimeout(() => {
      clearInterval(interval)
      console.log('[SmartModalBlocker] Stopped monitoring - user can now open modals freely');
    }, 5000)
    
    return () => {
      clearInterval(interval)
      document.removeEventListener('click', handleClick, true)
      document.removeEventListener('mousedown', handleClick, true)
    }
  }, [pathname])
  
  return null
}