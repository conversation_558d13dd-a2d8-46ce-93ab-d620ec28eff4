'use client';
import { useEffect, useMemo, useState, useTransition } from 'react';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import * as AuthAction from '@/actions/auth';
import { useToast } from '@/components/ui/useToast';
import { Input } from '@/components/ui/Input';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { verifyCodeValidation } from '@/validations';
import { cn } from '@/lib/utils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import EmailTab from './components/email-tab';
import Link from 'next/link';
import dynamic from 'next/dynamic';
import { useWalletAuth } from '@/hooks/useWalletAuth';
import { SmartModalBlocker } from './SmartModalBlocker';
import { TrustWalletSmartBlocker } from './TrustWalletSmartBlocker';

const WalletButtons = dynamic(() => import('@/components/wallet/WalletButtons'), {
  ssr: false,
  loading: () => <div className="text-center py-4">Loading wallets...</div>
});

interface Props {
  callback?: string;
  paramsCode?: string | null;
}

const Login = ({ callback, paramsCode }: Props) => {
  const t = useTranslations('');
  const { toast } = useToast();
  const [code, setCode] = useState({ value: '', isApplied: false });
  const [isVerifying, setVerifyTransition] = useTransition();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(() => {
    return tabParam === 'email' ? 'email' : 'wallet';
  });
  const successMessage = searchParams.get('register') === 'success';
  const initialValues = useMemo(() => ({ code: code.value }), [code.value]);

  const { isAuthenticating } = useWalletAuth({
    callback,
    referralCode: code.isApplied ? code.value : undefined,
  });

  const onVerifyCode = (values: any, actions?: any) => {
    if (!values?.code) return;
    setVerifyTransition(async () => {
      const referralCode = values?.code?.trim()?.toUpperCase();
      const { status, errors, data } = await AuthAction.verifyCode({ code: referralCode });
      if (status !== 'success') {
        actions
          ? actions.setErrors(errors)
          : toast({
            variant: 'error',
            description: errors?.code,
          });
        return;
      }
      setCode({ value: referralCode, isApplied: true });
      toast({
        variant: 'success',
        description: data.message,
      });
    });
  };

  const handleChangeButton = (formik: any) => {
    setCode({ value: '', isApplied: false });
    formik.setFieldValue('code', '');
  };

  useEffect(() => {
    if (paramsCode && !code.isApplied) {
      onVerifyCode({ code: paramsCode });
    }
  }, [paramsCode, code.isApplied]);

  return (
    <>
      {/* Smart blocking - prevents auto-open but allows manual connection */}
      <SmartModalBlocker />
      <TrustWalletSmartBlocker />
      <div className="login-page flex justify-center items-center w-full md:py-20 py-10 z-50 bg-[#09002F]">
        <div className="relative p-4 w-full max-h-full overflow-auto">
        <div className='max-w-4xl mx-auto'>
          {successMessage && (
            <div className="bg-[#04645C] border border-[#00C888] text-white p-3 rounded font-semibold mb-10 text-center">
              {t('login.authentication_success')}
            </div>
          )}
        </div>
        <div className="max-w-2xl mx-auto">
          <div className="relative bg-[#FFFFFF0D] text-white shadow-[0_3px_10px_rgba(255,255,255,0.5)] rounded-md mt-2">
            <div className="w-full h-full flex flex-col justify-center items-center px-5 sm:py-8 py-6">
              <div className="text-center text-2xl font-bold">
                {t('login.login')}
              </div>
              <hr className="w-full h-px my-7 bg-[#3A3953] border-0"></hr>
              <div className='w-full space-y-6'>
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-2 gap-2 bg-transparent">
                    <TabsTrigger value="wallet" className="bg-[#6123DB]/30 text-white font-semibold data-[state=active]:bg-[#6123DB] data-[state=active]:text-white">
                      {t('login.log_with_wallet')}
                    </TabsTrigger>
                    <TabsTrigger value="email" className="bg-[#6123DB]/30 text-white font-semibold data-[state=active]:bg-[#6123DB] data-[state=active]:text-white">
                      {t('login.email_address')}
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="wallet" className="space-y-4 mt-4">
                    <p className="text-sm font-medium">
                      {t('login.connect_wallet_give_better_access_assets')}
                    </p>
                    <div className="grid w-full gap-2">
                      <Formik
                        validateOnBlur={false}
                        validateOnChange={false}
                        enableReinitialize
                        initialValues={initialValues}
                        validationSchema={verifyCodeValidation(t)}
                        onSubmit={onVerifyCode}>
                        {formik => {
                          return (
                            <Form className="grid space-y-0.5 w-full">
                              <label className="text-sm font-medium">
                                {t('login.referral_code')}
                              </label>
                              <div className="flex gap-2">
                                <Field
                                  name="code"
                                  className={cn(
                                    'w-full text-left !text-sm disabled:opacity-50',
                                    code.isApplied && 'border-2 border-[#6123DB]',
                                  )}
                                  component={Input}
                                  disabled={isVerifying || code.isApplied}
                                  placeholder={t('profile.referral_code')}
                                  autoComplete="off"
                                />
                                {code.isApplied ? (
                                  <button
                                    type="button"
                                    className="disabled:opacity-50 text-center flex gap-2 sm:w-24 w-28  shrink-0 items-center justify-center bg-[#6123DB] text-white cursor-pointer px-4 rounded-sm"
                                    onClick={() => handleChangeButton(formik)}>
                                    {t('commons.remove')}
                                  </button>
                                ) : (
                                  <button
                                    type="submit"
                                    disabled={!formik.dirty}
                                    className="disabled:opacity-50 w-28 shrink-0 text-center flex gap-2 items-center justify-center bg-[#6123DB] text-white text-lg cursor-pointer px-4 rounded-sm">
                                    {isVerifying ? t('commons.checking') : t('commons.check')}
                                  </button>
                                )}
                              </div>
                              <span className="text-sm text-[#E30613]">
                                <ErrorMessage name="code" />
                              </span>
                            </Form>
                          );
                        }}
                      </Formik>
                      <WalletButtons disabled={isVerifying || isAuthenticating} />
                      <p className="text-sm font-medium mt-4">
                        {t('login.by_login_to_this_service')}&nbsp;
                        <span className='underline'>
                          <Link href={'/terms'} prefetch={false}>
                            {t('login.terms')}
                          </Link>
                        </span> {t('login.and')}&nbsp;
                        <span className='underline'>
                          <Link href={'/policy'} prefetch={false}>
                            {t('login.policy')}
                          </Link>
                        </span>
                        {t('login.are_deemed_agreed')}
                      </p>
                    </div>
                  </TabsContent>
                  <TabsContent value="email">
                    <EmailTab />
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </>
  );
};

export default Login;