'use client'

import { useEffect, useRef } from 'react'
import { usePathname } from 'next/navigation'

export function TrustWalletSmartBlocker() {
  const pathname = usePathname()
  const userClickedTrustRef = useRef(false)
  const hasBlockedTrustAutoOpen = useRef(false)
  
  useEffect(() => {
    if (!pathname?.includes('/auth/login')) return
    
    
    // Clear Trust Wallet specific keys on mount
    const clearTrustWalletKeys = () => {
      const trustKeys = [
        'wagmi.com.trustwallet.app.disconnected',
        'trust.disconnected',
        'wagmi.com.trustwallet.app',
        '@appkit/wallet_id',
        'WALLETCONNECT_DEEPLINK_CHOICE'
      ];
      
      trustKeys.forEach(key => {
        const value = localStorage.getItem(key);
        if (value && (value.includes('trust') || key.includes('trust'))) {
          localStorage.removeItem(key);
        }
      });
      
      // Also check for Trust in recent connectors
      const recentConnectorId = localStorage.getItem('wagmi.recentConnectorId');
      if (recentConnectorId && recentConnectorId.includes('trust')) {
        localStorage.removeItem('wagmi.recentConnectorId');
      }
    };
    
    // Initial clear
    clearTrustWalletKeys();
    
    // Track Trust Wallet button clicks specifically
    const handleClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      let element: HTMLElement | null = target;
      
      while (element) {
        const text = element.textContent?.toLowerCase() || '';
        
        if (text.includes('trust') && text.includes('wallet')) {
          userClickedTrustRef.current = true;
          
          // Clear any blocking state before user action
          localStorage.removeItem('trust.disconnected');
          localStorage.removeItem('wagmi.com.trustwallet.app.disconnected');
          
          // Keep state for 5 seconds
          setTimeout(() => {
            userClickedTrustRef.current = false;
          }, 5000);
          
          break;
        }
        
        element = element.parentElement;
      }
    };
    
    document.addEventListener('click', handleClick, true);
    document.addEventListener('mousedown', handleClick, true);
    
    // Monitor for Trust Wallet auto-open attempts
    const blockTrustAutoOpen = () => {
      // Check if user clicked Trust Wallet
      const userClicked = localStorage.getItem('trust_user_clicked') === 'true';
      
      // Only block if user hasn't clicked Trust Wallet
      if (!userClickedTrustRef.current && !userClicked && !hasBlockedTrustAutoOpen.current) {
        // Check if modal is trying to open with Trust Wallet
        const modalOpen = localStorage.getItem('@appkit/modal_open');
        const walletId = localStorage.getItem('@appkit/wallet_id');
        const activeView = localStorage.getItem('@appkit/active_view');
        
        if (modalOpen === 'true' && (walletId?.includes('trust') || activeView?.includes('trust'))) {
          // Close the modal
          localStorage.setItem('@appkit/modal_open', 'false');
          localStorage.removeItem('@appkit/active_view');
          localStorage.removeItem('@appkit/wallet_id');
          
          // Mark Trust as disconnected
          localStorage.setItem('trust.disconnected', 'true');
          localStorage.setItem('wagmi.com.trustwallet.app.disconnected', 'true');
          
          hasBlockedTrustAutoOpen.current = true;
          
          // Remove Trust modal from DOM
          setTimeout(() => {
            const modals = document.querySelectorAll('w3m-modal, appkit-modal, dialog');
            modals.forEach(modal => {
              const modalText = modal.textContent?.toLowerCase() || '';
              if (modalText.includes('trust')) {
                modal.remove();
              }
            });
          }, 100);
        }
      }
    };
    
    // Clear Trust keys periodically in first 3 seconds
    const clearIntervalId = setInterval(() => {
      clearTrustWalletKeys();
      blockTrustAutoOpen();
    }, 100);
    
    setTimeout(() => {
      clearInterval(clearIntervalId);
    }, 3000);
    
    // Continue monitoring for auto-opens less frequently
    const monitorInterval = setInterval(blockTrustAutoOpen, 500);
    
    setTimeout(() => {
      clearInterval(monitorInterval);
    }, 10000);
    
    return () => {
      clearInterval(clearIntervalId);
      clearInterval(monitorInterval);
      document.removeEventListener('click', handleClick, true);
      document.removeEventListener('mousedown', handleClick, true);
    };
  }, [pathname]);
  
  return null;
}