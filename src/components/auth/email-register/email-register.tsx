"use client"
import { useState } from "react"
import { Formik, Form, Field, ErrorMessage } from "formik"
import { Input } from "@/components/ui/Input"
import { Label } from "@/components/ui/label"
import { emailRegister } from "@/validations"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { useTranslations } from "next-intl"

interface FormValues {
  email: string
  password: string
}

export default function EmailRegister() {
  const t = useTranslations()
  const [isRegistered, setIsRegistered] = useState(false)

  const initialValues: FormValues = {
    email: "",
    password: "",
  }

  const handleSubmit = async (
    values: FormValues,
    { setSubmitting }: { setSubmitting: (isSubmitting: boolean) => void },
  ) => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      console.log("Registration data:", values)
      setIsRegistered(true)
    } catch (error) {
      console.error("Registration failed:", error)
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="w-full h-full min-h-screen bg-[#09002F] flex items-center justify-center p-4 sm:py-20 py-10">
      <div className="w-full max-w-xl mx-auto">
        <div className="w-full bg-[#FFFFFF0D] text-white shadow-[0_3px_10px_rgba(255,255,255,0.5)] rounded-md px-5 py-8">
          {
            !isRegistered ? (
              <>
                <div className="text-center">
                  <h1 className="text-white text-xl font-semibold">
                    {t('register.register_with_email_address')}
                  </h1>
                </div>
                <hr className="h-px my-5 bg-[#3A3953] border-0"></hr>
                <Formik initialValues={initialValues} validationSchema={emailRegister(t)} onSubmit={handleSubmit}>
                  {({ isSubmitting, errors, touched }) => (
                    <Form className="w-full space-y-3">
                      <div className="space-y-1">
                        <Label htmlFor="email" className="text-white text-sm">
                          {t('register.email_address')}
                        </Label>
                        <Field
                          name="email"
                          type="email"
                          placeholder={t('register.email_address')}
                          component={Input}
                          className='w-full bg-white text-left !text-sm' />
                        <ErrorMessage name="email" component="div" className="text-red-600 text-sm mt-1" />
                      </div>

                      <div className="space-y-1">
                        <Label htmlFor="password" className="text-white text-sm">
                          {t('register.password')}
                        </Label>
                        <Field
                          name="password"
                          type="password"
                          placeholder={t('register.password')}
                          component={Input}
                          className='w-full bg-white text-left !text-sm' />
                        <ErrorMessage name="password" component="div" className="text-red-600 text-sm mt-1" />
                      </div>
                      <div className="pt-5">
                        <Button
                          type="submit"
                          disabled={isSubmitting}
                          className="w-full h-12 bg-[#7D69FF] disabled:[#7D69FF]/50 text-white font-semibold rounded-md transition-colors hover:bg-[#5f4fc7]"
                        >
                          {isSubmitting ? `${t('register.registering')}...` : t('register.registration')}
                        </Button>
                      </div>
                    </Form>
                  )}
                </Formik>
              </>
            ) : (
              <div>
                <h1 className="text-white text-2xl text-center font-semibold">
                  {t('register.registration_complete')}
                </h1>
                <hr className="h-px my-8 bg-[#3A3953] border-0"></hr>
                <div className="space-y-3 text-white text-sm leading-relaxed">
                  <p>{t('register.registration_is_complete')}</p>
                  <p>
                    {t('register.we_have_send_you_a_verification_email_please_follow_link_provided')}
                    <br />
                    {t('register.access_and_activate_your_account')}
                  </p>
                </div>
                <div className="flex pt-5">
                  <Link href={'/profile'} prefetch={false} className="font-semibold underline cursor-pointer">
                    {t('register.back_to_profile')}
                  </Link>
                </div>
              </div>
            )
          }
        </div>
      </div>
    </div>
  )
}