'use client'
import React, { useState } from 'react'
import { useTranslations } from 'next-intl'
import { ErrorMessage, Field, Form, Formik } from 'formik'
import { Input } from '@/components/ui/Input'
import { emailRegister } from '@/validations'
import { Button } from '@/components/ui/button'
import { Link, useRouter } from '@/hooks/navigation'

const Register = () => {
  const t = useTranslations('register')
  const [showSuccess, setShowSuccess] = useState(false)
  const router = useRouter()

  const handleSignup = async (values: { email: string; password: string; verificationCode: string }) => {
    try {
      console.log("Signup values:", values)

      await new Promise((resolve) => setTimeout(resolve, 1000))
      setShowSuccess(true)

      setTimeout(() => {
        setShowSuccess(false)
        router.push("/auth/login?tab=email&register=success")
      }, 3000)
    } catch (error) {
      console.error("Signup error:", error)
    }
  }

  return (
    <div className="flex justify-center items-center w-full md:py-20 py-10 z-50 bg-[#09002F]">
      <div className="relative p-4 w-full max-w-2xl max-h-full overflow-auto">
        <div className="bg-[#FFFFFF0D] text-white shadow-[0_3px_10px_rgba(255,255,255,0.5)] rounded-lg px-5 py-8 w-full relative">
          {
            showSuccess ? (
              <>
                <h2 className="text-white text-xl font-bold mb-4">
                  {t('registration_complete')}
                </h2>
                <hr className="w-full h-px my-8 bg-[#3A3953] border-0"></hr>
                <div className="text-white text-sm space-y-2">
                  <p>{t('registration_is_complete')}</p>
                  <p>
                    {t('we_have_send_you_a_verification_email')}
                  </p>
                </div>
              </>
            ) : (
              <>
                <h2 className="text-white text-xl font-bold text-center mb-6">
                  {t('sign_up')}
                </h2>
                <hr className="w-full h-px my-8 bg-[#3A3953] border-0"></hr>
                <Formik
                  initialValues={{ email: "", password: "", verificationCode: "" }}
                  validationSchema={emailRegister(t)}
                  onSubmit={handleSignup}
                >
                  {({ isSubmitting }) => (
                    <Form className="space-y-5">
                      <div>
                        <label className="text-white text-sm font-medium block mb-1">
                          {t('email_address')} <span className="text-[#E30613] text-xs bg-[#530B2E] border border-[#E30613] rounded-md px-1 ml-2">{t('required')}</span>
                        </label>
                        <Field
                          name="email"
                          type="email"
                          placeholder={t('email_address')}
                          component={Input}
                          className='w-full bg-white text-left !text-sm' />
                        <ErrorMessage name="email" component="div" className="text-[#E30613] text-sm mt-1" />
                      </div>

                      <div>
                        <label className="text-white text-sm font-medium block mb-1">
                          {t('password')} <span className="text-[#E30613] text-xs bg-[#530B2E] border border-[#E30613] rounded-md px-1 ml-2">{t('required')}</span>
                        </label>
                        <Field
                          name="password"
                          type="password"
                          placeholder={t('password')}
                          component={Input}
                          className='w-full bg-white text-left !text-sm'
                        />
                        <ErrorMessage name="password" component="div" className="text-[#E30613] text-sm mt-1" />
                      </div>

                      <div>
                        <label className="text-white text-sm font-medium block mb-1">
                          {t('authentication_code')}
                        </label>
                        <div className="flex gap-2">
                          <Field
                            name="verificationCode"
                            type="text"
                            placeholder={t('authentication_code')}
                            component={Input}
                            className='w-full bg-white text-left !text-sm'
                          />
                          <Button type="button" className="shrink-0 h-full bg-[#6123DB] hover:bg-[#401596] text-white px-4 py-3 rounded">
                            {t('send')}
                          </Button>
                        </div>
                        <ErrorMessage name="verificationCode" component="div" className="text-[#E30613] text-sm mt-1" />
                      </div>

                      <div className="text-xs py-3">
                        {t('by_register_for_service')}&nbsp;
                        <span className='underline'>
                          <Link href={`/terms`} prefetch={false}>
                            {t("terms_of_service")}
                          </Link>
                        </span>&nbsp;
                        {t('and')}&nbsp;
                        <span className='underline'>
                          <Link href={`/policy`} prefetch={false}>
                            {t('privacy_policy')}
                          </Link>
                        </span>
                        {t('are_deemed_agreed')}
                      </div>

                      <Button
                        type="submit"
                        className="w-full h-full bg-[#7D69FF] hover:bg-[#5f4fc7] text-white font-semibold py-3 text-lg rounded mt-6"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? `${t('registering')}...` : t('registration')}
                      </Button>
                      <div className='pt-3 pb-4'>
                        <hr className="w-full h-px bg-[#3A3953] border-0"></hr>
                      </div>
                      <div className="text-center">
                        <Link
                          href={'/auth/login'}
                          prefetch={false}
                          className="text-[#7D69FF] font-semibold hover:underline"
                        >
                          {t('if_you_have_account_click_here')}
                        </Link>
                      </div>

                      <div className="text-center">
                        <Link
                          href={'/auth/login'}
                          prefetch={false}
                          className="text-[#7D69FF] font-semibold hover:underline"
                        >
                          {t('login_with_wallet')}
                        </Link>
                      </div>
                    </Form>
                  )}
                </Formik>
              </>
            )
          }
        </div>
      </div>
    </div>
  )
}

export default Register