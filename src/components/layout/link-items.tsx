'use client'
import clsx from 'clsx';
import React from 'react';
import { Link, usePathname } from '@/hooks/navigation';

const LinkItems = () => {
  const pathname = usePathname()
  const listItems = [
    {
      name: 'イベント／キャンペーン',
      url: '/events'
    },
    {
      name: 'ランキング',
      url: '/ranking'
    },
    {
      name: '新着／配信予定',
      url: '/upcoming'
    }
  ]

  return (
    <div className='w-full bg-[#0C0420] border-b border-[#1D1A4B] text-white px-5'>
      <div className='flex items-center gap-10 overflow-auto pb-2'>
        {
          listItems?.map((item, index) => {
            const isActive =
              item?.url === "/"
                ? pathname === "/"
                : pathname.startsWith(item?.url);
            return (
              (
                <Link
                  key={index}
                  href={item?.url}
                  prefetch={false}
                  className={clsx(
                    'text-[15px] whitespace-nowrap',
                    isActive ? 'underline  underline-offset-[13px] decoration-2 decoration-[#7D69FF]' : ''
                  )}>
                  {item?.name}
                </Link>
              )
            )
          })
        }
      </div>
    </div>
  )
}

export default LinkItems