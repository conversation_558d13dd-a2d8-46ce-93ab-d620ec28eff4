import React from 'react'
import Link from "next/link"
import Image from "next/image"
import logo from '@/assets/images/logo.webp'
import { useTranslations } from 'next-intl'
import { sliceIntoChunks } from '@/lib/utils'

const Footer = ({ categories, chains }: { categories: any, chains: any }) => {
  const t = useTranslations()
  const events = [
    {
      name: t('footer.ranking'),
      url: "/ranking"
    },
    {
      name: t('footer.new_releases'),
      url: '/new-releases'
    }
  ]
  const categoryData = sliceIntoChunks(categories, 4)

  return (
    <footer className="w-full bg-[#0C0420] text-[#86898E]">
      <div className="py-10">
        <div className='container mx-auto px-5'>
          <div className='w-full flex lg:flex-row flex-col gap-x-5 gap-y-8'>
            <div className='lg:w-2/5 w-full'>
              <div className="flex">
                <Link href='/' prefetch={false}>
                  <Image src={logo} alt='' className='w-auto sm:h-6 h-5' />
                </Link>
              </div>
            </div>
            <div className='lg:w-3/5 w-full'>
              <div className='w-full grid md:grid-cols-4 sm:grid-cols-2 grid-cols-1 gap-5'>
                <div className="space-y-4">
                  <h3 className="text-sm font-semibold text-white">
                    {t('footer.events_or_campaigns')}
                  </h3>
                  <ul className="font-semibold space-y-3">
                    {
                      events?.map((item, eventIndex) => (
                        <li key={eventIndex}>
                          <Link href={item?.url} prefetch={false} className="text-sm text-white transition-colors">
                            {item?.name}
                          </Link>
                        </li>
                      ))
                    }
                  </ul>
                </div>
                <div className="space-y-4 lg:col-span-2  gap-4">
                  <h3 className="text-sm font-semibold text-white">
                    {t('footer.category')}
                  </h3>
                  <div className='grid lg:grid-cols-2 gap-4'>
                    {
                      categoryData.map((item, index) => (
                        <ul key={index} className="space-y-3">
                          {
                            item.map((item, categoryIndex) => (
                              <li key={categoryIndex}>
                                <Link href={`/categories/${item?.slug}`} prefetch={false} className="text-sm transition-colors">
                                  {item?.name}
                                </Link>
                              </li>
                            ))
                          }
                        </ul>
                      ))
                    }
                  </div>
                </div>
                <div className="space-y-4">
                  <h3 className="text-sm font-semibold text-white">
                    {t('footer.chain')}
                  </h3>
                  <ul className="space-y-3">
                    {
                      chains?.map((item: any, chainIndex: number) => (
                        <li key={chainIndex}>
                          <Link href={`/chains/${item?.slug}`} className="text-sm transition-colors">
                            {item?.name}
                          </Link>
                        </li>
                      ))
                    }
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <hr className="h-px my-3 bg-[#3A3953] border-0"></hr>
      <div className='container mx-auto px-5 pb-2'>
        <div className="border-gray-700">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm">© ClubX</div>
            <div className="flex flex-wrap gap-6 text-sm">
              <Link href="/abouts" className="transition-colors">
                {t("footer.what's_club_x")}
              </Link>
              <Link href="/first-time-visitors" className="transition-colors">
                {t('footer.first_time_vistor')}
              </Link>
              <Link href="/terms" className="transition-colors">
                {t('footer.terms_of_service')}
              </Link>
              <Link href="/policy" className="transition-colors">
                {t('footer.privacy_policy')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
