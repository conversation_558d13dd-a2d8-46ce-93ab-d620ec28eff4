import React from 'react';
import Header from './Header';
import Footer from './Footer';

const Layout = ({ children, categories, chains }: { children: React.ReactNode, categories: any, chains: any }) => {
  return (
    <div>
      <Header />
      <div className='bg-[#09002F] min-h-screen -mt-[1px]'>
        {children}
      </div>
      <Footer categories={categories} chains={chains} />
    </div>
  );
};

export default Layout;
