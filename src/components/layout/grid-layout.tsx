"use client"
import <PERSON><PERSON>oader from '@/components/common/ImageLoader'
import { Link } from '@/hooks/navigation';

type Props = {
  children: React.ReactNode;
  categories: {
    icon: string;
    name: string;
    slug: string;
  }[];
  chains: {
    icon: string;
    name: string;
    slug: string;
  }[];
}

const GridLayout = ({ children, categories, chains }: Props) => {
  return (
    <div className="min-h-screen bg-[#09002F] text-white">
      <div className="xl:px-12 md:px-10 px-5 p-14">
        <div className="flex lg:flex-row flex-col gap-x-12 gap-y-16">
          <div className="lg:w-52 w-full">
            <div className='flex flex-col lg:gap-5 gap-4'>
              <>
                <h2 className="text-lg font-bold">カテゴリ</h2>
                <nav className="w-full flex lg:flex-col flex-row gap-x-8 gap-y-4 overflow-auto">
                  {categories?.map((item, index) => (
                    <Link
                      key={index}
                      href={`/categories/${item.slug}`}
                      prefetch={false}
                      className="w-auto flex items-center gap-3 text-left whitespace-nowrap hover:bg-white/10 transition-colors text-sm"
                    >
                      <div className='w-6 h-6 shrink-0'>
                        <ImageLoader width={24} height={24} src={item?.icon} alt={item?.name} className='w-full h-full' />
                      </div>
                      {item?.name}
                    </Link>
                  ))}
                </nav>
              </>
              <hr className="w-full h-px my-2 bg-[#3A3953] border-0"></hr>
              <>
                <h2 className="text-lg font-bold">チェーン</h2>
                <nav className="w-full flex lg:flex-col flex-row gap-x-8 gap-y-4 overflow-auto">
                  {chains?.map((item, chainIndex) => (
                    <Link
                      key={chainIndex}
                      href={`/chains/${item.slug}`}
                      prefetch={false}
                      className="w-auto flex items-center gap-3 text-left whitespace-nowrap hover:bg-white/10 transition-colors text-sm"
                    >
                      <div className='w-6 h-6 shrink-0'>
                        <ImageLoader width={24} height={24} src={item?.icon} alt={item?.name} className='w-full h-full' />
                      </div>
                      {item?.name}
                    </Link>
                  ))}
                </nav>
              </>
            </div>
          </div>

          <div className="flex-1 flex flex-col space-y-16 overflow-auto">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}

export default GridLayout