'use client'
import React, { useEffect, useState } from 'react'
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as Tabs from '@radix-ui/react-tabs';

const TabCategories = ({ children, data }: any) => {
  const pathName = usePathname()
  const defaultTab = pathName
  const [active, setActive] = useState('categoies')

  useEffect(() => {
    const categorySlug = pathName.split('/').pop();
    setActive(categorySlug || "categoies")
  }, [pathName])
  return (
    <div className="flex flex-wrap items-center gap-3">
      <Tabs.Root className="TabsRoot w-full h-full" defaultValue={defaultTab}>
        <Tabs.List className="TabsList space-x-1 flex overflow-auto bg-black" aria-label="Category Tabs">
          {[{id: 'all', slug: 'categories', name: 'All'}, ...data].map((item: any, index: any) => {
            return (
              <Link href={item?.id == 'all' ? '/categories' : `/categories/${item?.slug}`} key={item.id}>
                <Tabs.Trigger
                  className={`TabsTrigger px-5 py-2 text-lg whitespace-nowrap font-bold
                  text-white
                  ${active === item?.slug ? 'z-10 text-white shadow-[inset_0px_-4px_5px_-2px_rgba(2,_184,_215,_1)]' : ''}
                `}
                  value={item.name}>
                  {item.name}
                </Tabs.Trigger>
              </Link>
            )
          })}
        </Tabs.List>
        {children}
      </Tabs.Root>
    </div>
  )
}

export default TabCategories