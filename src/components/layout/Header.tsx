'use client';
import Link from 'next/link';
import Image from 'next/image';
import { FaUser } from "react-icons/fa";
import { isEmpty, keyBy } from 'lodash';
import { useTranslations } from 'next-intl';
import { RiSearchLine } from "react-icons/ri";
import React, { useEffect, useState } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import logo from '@/assets/images/logo.webp';
import { Input } from "@/components/ui/Input";
import point from '@/assets/images/card-wallet.svg';
import useSession from '@/components/session/useSession';
import SelectLanguage from '@/components/common/select-language';


const Header = () => {
  const { session } = useSession();
  const wallets = keyBy(session.wallets, 'type');
  const [isLogin, setIsLogin] = useState(false);
  const router = useRouter();
  const code = useSearchParams().get('code');
  const pathname = usePathname();
  const t = useTranslations()

  const formattedClubXPoint = Intl.NumberFormat('en', { currency: 'USD' }).format(wallets?.clubx_point?.balance || 0);

  const handleSession = () => {
    if (isEmpty(session)) {
      router.push('/auth/login')
    } else {
      router.push('/profile');
    }
  };

  useEffect(() => {
    if (isLogin) {
      document.body.classList.add('no-scroll');
    } else {
      document.body.classList.remove('no-scroll');
    }
    return () => {
      document.body.classList.remove('no-scroll');
    };
  }, [isLogin]);

  useEffect(() => {
    if (code && isEmpty(session)) {
      setIsLogin(true);
    } else {
      router.replace(pathname);
    }
  }, []);

  // const handleClose = () => {
  //   setIsLogin(false);
  //   if (code) {
  //     router.replace(pathname);
  //   }
  // };

  return (
    <div>
      <div className="relative w-full h-20 flex items-center justify-between bg-[#0C0420] text-white px-4 pb-3 gap-5">
        <Link href="/" prefetch={false}>
          <Image src={logo} alt="" className="w-auto sm:h-6 h-5" />
        </Link>
        <div className="md:block hidden flex-1 max-w-[400px] mx-8">
          <div className="relative">
            <RiSearchLine className="absolute left-5 top-1/2 transform -translate-y-1/2 text-black w-5 h-5" />
            <Input
              type="search"
              placeholder="キーワード検索"
              className="w-full pl-12 pr-5 py-[10px] bg-white text-sm text-[#A5A5A5] border-0 rounded-full focus:ring-2 focus:ring-[#7D69FF]"
            />
          </div>
        </div>
        <div className="h-full flex items-center justify-end gap-3">
          {
            isEmpty(session) === true && (
              <Link href={'/auth/login'} prefetch={false} className='bg-[#7D69FF] font-medium rounded-md px-6 py-1.5'>
                {t('header.login')}
              </Link>
            )
          }
          {
            isEmpty(session) === false && (
              <div className='flex items-center gap-5'>
                <div className="flex items-center gap-1 cursor-pointer">
                  <Image src={point} alt="" className="w-auto h-[38px]" />
                  {
                    session.wallet_address ? (
                      <>
                        {wallets && (
                          <div className="sm:text-lg text-sm whitespace-nowrap font-bold">
                            {formattedClubXPoint} <span className="text-sm">P</span>
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="sm:text-lg text-sm whitespace-nowrap font-bold">
                        {t('header.not_connected')}
                      </div>
                    )
                  }
                </div>
                <div onClick={handleSession} className="bg-[#7D69FF] rounded-full p-[9px] cursor-pointer">
                  <FaUser size={20} />
                </div>
              </div>
            )
          }
          <SelectLanguage />
        </div>
      </div>
    </div >
  );
};

export default Header;
