'use client'

import { useEffect, useState, memo } from 'react'
import { useAppKit, useAppKitState } from '@reown/appkit/react'
import { useConnect } from 'wagmi'
import Image from 'next/image'
import walletLogo from '@/assets/images/login-image/スクリーンショット 2025-06-11 17.30.56 2.png'

interface WalletConnectDirectQRProps {
  disabled?: boolean
}

function WalletConnectDirectQR({ disabled }: WalletConnectDirectQRProps) {
  const { open } = useAppKit()
  const { selectedNetworkId } = useAppKitState()
  const { connectors } = useConnect()
  const [isOpening, setIsOpening] = useState(false)

  const handleClick = async () => {
    console.log('Opening WalletConnect QR code')
    setIsOpening(true)

    try {
      // Find WalletConnect connector
      const wcConnector = connectors.find(c =>
        c.id === 'walletConnect' ||
        c.name === 'WalletConnect' ||
        c.id.toLowerCase().includes('walletconnect')
      )

      console.log('WalletConnect connector:', wcConnector?.name || 'not found')
      console.log('Available connectors:', connectors.map(c => ({ id: c.id, name: c.name })))

      // Open modal - AppKit should automatically show QR for WalletConnect
      try {
        await open({
          view: 'ConnectingWalletConnectBasic' // Try specific WalletConnect view
        })
      } catch (err) {
        // Fallback to regular Connect view
        await open({ view: 'Connect' })
      }

    } catch (error) {
      console.error('Error opening WalletConnect:', error)
      // Try alternative approach
      try {
        await open() // Open with default view
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError)
      }
    } finally {
      setIsOpening(false)
    }
  }

  return (
    <button
      type="button"
      onClick={handleClick}
      disabled={disabled || isOpening}
      className='w-full bg-[#211943] text-white p-3 hover:bg-[#2a1f52] disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-lg'
    >
      <div className='flex items-center justify-between gap-5'>
        <div className='flex items-center gap-5'>
          <div className='w-10 h-10 shrink-0 rounded-lg overflow-hidden'>
            <Image
              width={40}
              height={40}
              src={walletLogo}
              alt='wallet-logo'
              className='w-full h-full object-cover'
            />
          </div>
          <div>{isOpening ? 'Opening QR...' : 'WalletConnect'}</div>
        </div>
        <div className='bg-[#20253B] text-[#5673FF] text-xs font-semibold px-1.5 py-1 rounded-md uppercase'>
          QR CODE
        </div>
      </div>
    </button>
  )
}

export default memo(WalletConnectDirectQR)