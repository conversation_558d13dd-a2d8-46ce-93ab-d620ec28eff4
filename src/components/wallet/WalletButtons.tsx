'use client'

import { memo } from 'react'
import { useAppKit } from '@reown/appkit/react'
import Image from 'next/image'
import SimpleMetaMaskButton from './SimpleMetaMaskButton'
import WalletConnectDirectQR from './WalletConnectDirectQR'
import SimpleTrustWalletButton from './SimpleTrustWalletButton'
import allWalletsLogo from '@/assets/images/login-image/Mask group.png'

interface WalletButtonsProps {
  disabled?: boolean
}

function WalletButtons({ disabled }: WalletButtonsProps) {
  const { open } = useAppKit()

  return (
    <div className='flex flex-col gap-3 py-3'>
      <WalletConnectDirectQR disabled={disabled} />
      <SimpleMetaMaskButton disabled={disabled} />
      <SimpleTrustWalletButton disabled={disabled} />
      <button
        onClick={() => open({ view: 'AllWallets' })}
        disabled={disabled}
        className='w-full h-full bg-[#211943] text-white p-3 hover:bg-[#2a1f52] disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-lg'
      >
        <div className='w-full flex items-center justify-between gap-5'>
          <div className='flex items-center gap-5'>
            <div className='w-10 h-10 shrink-0 rounded-lg overflow-hidden'>
              <Image width={40} height={40} src={allWalletsLogo} alt='all-wallet-logo' className='w-full h-full object-cover' />
            </div>
            <div>All Wallets</div>
          </div>
          <div className='bg-[#2E2F2F] text-[#939D9D] text-xs font-semibold px-1.5 py-1 rounded-md'>120+</div>
        </div>
      </button>
    </div>
  )
}

export default memo(WalletButtons)