'use client'

import { useState, useEffect, memo } from 'react'
import { useConnect } from 'wagmi'
import Image from 'next/image'
import metamaskLogo from '@/assets/images/login-image/スクリーンショット 2025-06-11 17.30.56 3.png'

interface SimpleMetaMaskButtonProps {
  disabled?: boolean
}

function SimpleMetaMaskButton({ disabled }: SimpleMetaMaskButtonProps) {
  const { connect, connectors } = useConnect()
  const [isConnecting, setIsConnecting] = useState(false)
  const [hasMetaMask, setHasMetaMask] = useState(false)

  useEffect(() => {
    // Check if MetaMask is available
    const checkMetaMask = () => {
      if (typeof window !== 'undefined' && window.ethereum) {
        const isMetaMask = !!(window.ethereum.isMetaMask && !window.ethereum.isBraveWallet)
        setHasMetaMask(isMetaMask)
      }
    }

    checkMetaMask()
    // Check again after a delay in case of slow injection
    setTimeout(checkMetaMask, 1000)
  }, [])

  const handleClick = async () => {
    console.log('MetaMask button clicked')

    // If no MetaMask, open download page
    if (!hasMetaMask) {
      console.log('MetaMask not detected, opening download page')
      window.open('https://metamask.io/download/', '_blank')
      return
    }

    setIsConnecting(true)

    try {
      // Method 1: Try to find MetaMask connector
      const metaMaskConnector = connectors.find(c =>
        c.id === 'metaMask' ||
        c.name === 'MetaMask' ||
        (c.id === 'com.metamask' || c.id === 'io.metamask')
      )

      if (metaMaskConnector) {
        console.log('Found MetaMask connector:', metaMaskConnector.name)
        await connect({ connector: metaMaskConnector })
        return
      }

      // Method 2: Use injected connector
      const injectedConnector = connectors.find(c => c.id === 'injected')
      if (injectedConnector && hasMetaMask) {
        console.log('Using injected connector for MetaMask')
        await connect({ connector: injectedConnector })
        return
      }

      console.log('No suitable connector found')
      console.log('Available connectors:', connectors.map(c => ({ id: c.id, name: c.name })))

    } catch (error) {
      console.error('Connection error:', error)
    } finally {
      setIsConnecting(false)
    }
  }

  return (
    <button
      type="button"
      onClick={handleClick}
      disabled={disabled || isConnecting}
      className='w-full bg-[#211943] text-white p-3 hover:bg-[#2a1f52] disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-lg'
    >
      <div className='flex items-center justify-between gap-5'>
        <div className='flex items-center gap-5'>
          <div className='w-10 h-10 shrink-0 rounded-lg overflow-hidden'>
            <Image
              width={40}
              height={40}
              src={metamaskLogo}
              alt='metamask-logo'
              className='w-full h-full object-cover'
            />
          </div>
          <div>{isConnecting ? 'Connecting...' : 'MetaMask'}</div>
        </div>
        {hasMetaMask && (
          <div className='bg-[#1F3A28] text-[#25D962] text-xs font-semibold px-1.5 py-1 rounded-md uppercase'>
            Installed
          </div>
        )}
      </div>
    </button>
  )
}

export default memo(SimpleMetaMaskButton)