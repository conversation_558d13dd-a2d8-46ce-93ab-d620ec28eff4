'use client'

import { useState, memo } from 'react'
import { useTrustWalletButton } from '@/hooks/useTrustWalletButton'
import Image from 'next/image'
import trustWalletLogo from '@/assets/images/login-image/スクリーンショット 2025-06-11 17.30.56 4.png'

interface SimpleTrustWalletButtonProps {
  disabled?: boolean
}

function SimpleTrustWalletButton({ disabled }: SimpleTrustWalletButtonProps) {
  const { handleTrustWalletClick } = useTrustWalletButton()
  const [isConnecting, setIsConnecting] = useState(false)

  const handleClick = async () => {
    setIsConnecting(true)
    try {
      await handleTrustWalletClick()
    } finally {
      setTimeout(() => setIsConnecting(false), 2000)
    }
  }

  const isTrustInstalled = typeof window !== 'undefined' && window.ethereum?.isTrust

  return (
    <button
      type="button"
      onClick={handleClick}
      disabled={disabled || isConnecting}
      className='w-full bg-[#211943] text-white p-3 hover:bg-[#2a1f52] disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-lg'
    >
      <div className='flex items-center justify-between gap-5'>
        <div className='flex items-center gap-5'>
          <div className='w-10 h-10 shrink-0 rounded-lg overflow-hidden'>
            <Image
              width={40}
              height={40}
              src={trustWalletLogo}
              alt='trust-wallet-logo'
              className='w-full h-full object-cover'
            />
          </div>
          <div>{isConnecting ? 'Opening Trust Wallet...' : 'Trust Wallet'}</div>
        </div>
        {isTrustInstalled ? (
          <div className='bg-[#1F3A28] text-[#25D962] text-xs font-semibold px-1.5 py-1 rounded-md uppercase'>
            Installed
          </div>
        ) : null}
      </div>
    </button>
  )
}

export default memo(SimpleTrustWalletButton)