'use client';
import { Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React, { useEffect, useMemo, useState, useTransition } from 'react';
import { Button } from '@/components/ui/button';
import PageCard from '@/components/common/page-card';
import { useBreadcrumb } from '@/providers/BreadcrumbProvider';
import { fetchGamesCategories, fetchGamesChains } from '@/actions/home';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const Category = ({ data, meta, slug, typeData, type = 'category' }: any) => {
  const t = useTranslations();
  const [games, setGames] = useState(data)
  const [paging, setPaging] = useState(meta)
  const [isPending, startTransition] = useTransition()
  const category = useMemo(() => {
    return typeData?.find((item: any) => item?.slug === slug)
  }, [slug, typeData])
  const { setBreadcrumb } = useBreadcrumb();
  const handleShowMore = async () => {
    startTransition(async () => {
      let newData: [], newMeta;
      let nextPage = Number(paging?.currentPage + 2);
      if (type === 'chain') {
        const resp = await fetchGamesChains({ slug, page: nextPage })
        newData = resp?.data
        newMeta = resp?.meta
      } else {
        const resp = await fetchGamesCategories({ slug, page: nextPage })
        newData = resp?.data
        newMeta = resp?.meta
      }
      setGames((prev: any) => [...prev, ...newData])
      setPaging(newMeta)
    })
  }
  useEffect(() => {
    setBreadcrumb(
      <Breadcrumb className="flex text-[#6D6D6D] pt-2.5">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/" className='uppercase'>Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage className="font-bold text-[#6D6D6D]">{category.name}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }, [slug, typeData])

  return (
    <div className='flex flex-col gap-4'>
      <header className="flex items-center justify-between">
        <div className="text-2xl md:text-3xl font-bold">{category.name}</div>
        <Select defaultValue="all">
          <SelectTrigger className="w-32 bg-white text-black border-0">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('play_earn_page.all')}</SelectItem>
          </SelectContent>
        </Select>
      </header>
      {
        games?.length > 0 ? (
          <div className='grid lg:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-5'>
            {
              games?.map((item: any, index: any) => (
                <PageCard key={index} slug={slug} data={item} />
              ))
            }
          </div>
        ) : (
          <div className='text-center text-3xl py-20'>
            {t('play_earn_page.there_are_no_play_earn_post_yet')}
          </div>
        )
      }

      {
        paging?.currentPage + 2 <= paging?.totalPage && (
          <div className="flex justify-center">
            <Button
              type="button"
              onClick={handleShowMore}
              disabled={isPending}
              className="text-white font-semibold py-6 px-32 bg-[#5f4fc7]">
              {
                isPending ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  t('play_earn_page.show_more')
                )
              }
            </Button>
          </div>
        )
      }
    </div>
  );
};

export default Category;
