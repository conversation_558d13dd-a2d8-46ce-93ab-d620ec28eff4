'use client'
import { useTranslations } from 'next-intl'
import React, { useEffect, useMemo, useState, useTransition } from 'react'
import { Button } from '@/components/ui/button'
import ListCard from '@/components/common/list-card'
import { useBreadcrumb } from '@/providers/BreadcrumbProvider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { fetchRanking } from '@/actions/home'
import { Loader2 } from 'lucide-react'

const Ranking = ({ data, meta, categories }: any) => {
  const t = useTranslations()
  const { setBreadcrumb } = useBreadcrumb();
  const [category, setCategory] = useState("all")
  const [games, setGames] = useState(data)
  const [paging, setPaging] = useState(meta)
  const [isPending, startTransition] = useTransition()
  const categoryItems = useMemo(() => {
    const items = [{ value: "all", label: "すべて" }]
    for (const item of categories) {
      items.push({
        value: item?.id,
        label: item?.name
      })
    }
    return items;
  }, [categories]);

  const fetchGames = async (page: number, category: string) => {
    startTransition(async () => {
      const params: any = {
        page: page,
      }
      if (category !== 'all') {
        params.category_id = category
      }
      const resp = await fetchRanking(params)
      if (page === 1) {
        setGames(resp?.data)
      } else {
        setGames((prev: any) => [...prev, ...resp?.data])
      }
      setPaging(resp?.meta)
    })
  }
  const handleShowMore = async () => {
    const nextPage = Number(paging?.currentPage + 2);
    await fetchGames(nextPage, category)
  }
  const handleChangeCategory = async (value: string) => {
    setCategory(value)
    await fetchGames(1, value)
  }

  useEffect(() => {
    setBreadcrumb(
      <Breadcrumb className="flex text-[#6D6D6D] pt-2.5">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/" className='uppercase'>Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage className="font-bold text-[#6D6D6D]">
              {t('ranking_page.ranking')}
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }, [])

  return (
    <>
      <div className='flex flex-col gap-4'>
        <header className="flex sm:flex-row flex-col gap-3 items-center justify-between">
          <div className="text-2xl md:text-3xl font-bold capitalize">
            {
              category === 'all' ? t('ranking_page.total_ranking') : `${categoryItems.find((cat: any) => cat.value === category)?.label} ${t('ranking_page.ranking')}`
            }
          </div>
          <Select value={category} onValueChange={handleChangeCategory}>
            <SelectTrigger className="w-32 bg-white text-black border-0">
              <SelectValue placeholder="カテゴリを選択" />
            </SelectTrigger>
            <SelectContent>
              {categoryItems.map((category) => (
                <SelectItem
                  key={category.value}
                  value={category.value}
                >
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </header>
        {
          games?.length > 0 ? (
            <div>
              <div className="overflow-auto">
                <div className="flex flex-col gap-0 min-w-[700px]">
                  {
                    games?.map((item: any, index: number) => (
                      <div key={index} className="w-full shrink-0">
                        <ListCard index={index + 1} data={item} />
                        <div className="flex justify-center">
                          <hr className="w-full h-px bg-[#3A3953] border-0 my-3" />
                        </div>
                      </div>
                    ))
                  }
                </div>
              </div>

              <div className="pt-14 text-center">
                {
                  paging?.currentPage + 2 <= paging?.totalPage && (
                    <Button
                      type='button'
                      disabled={isPending}
                      className="bg-[#7D69FF] text-white font-semibold px-10 hover:bg-[#5f4fc7]" onClick={handleShowMore}>
                      {
                        isPending ? <Loader2 className="w-4 h-4 animate-spin" /> : t('play_earn_page.show_more')
                      }
                    </Button>
                  )
                }
              </div>
            </div>
          ) : (
            <div className='text-center text-3xl py-20'>
              {t('ranking_page.no_ranking_post_yet')}
            </div>
          )
        }
      </div>
    </>
  )
}

export default Ranking