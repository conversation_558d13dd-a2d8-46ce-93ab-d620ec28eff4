'use client'
import Image from "next/image";
import { useEffect, useRef } from "react";
import { Link, useRouter } from "@/hooks/navigation";
import { Button } from "@/components/ui/button";
import { IoIosPlayCircle } from "react-icons/io";
import { Star, Heart, Edit } from "lucide-react";
import ReviewCard from "@/components/common/review-card";
import { useBreadcrumb } from "@/providers/BreadcrumbProvider";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useTranslations } from "next-intl";
import StartGameDialog from "../common/start-game-dialog";

const GameDetail = ({ slug, data }: any) => {
  const { setBreadcrumb } = useBreadcrumb();
  const t = useTranslations();
  const startGameDialogRef = useRef<any>(null);

  const reviews = [
    {
      id: 1,
      username: "ユーザーネーム",
      avatar: "/placeholder.svg?height=40&width=40",
      rating: 4,
      date: "2025.06.11",
      content:
        "2022年に「楽しい稼ぐ」コンセプトで大流行したSTEPNのシステムに触発され、「撮って稼ぐ」という新しいコンセプトを採用したブロックチェーンゲームがこのSNPITです。このゲームでは、カメラNFTを使用して写真を撮り、他のプレイヤーのNFTカメラと競争しながら、実世界で撮影することで報酬を得ることができます。私は、まだ始めたばかりなので、まだ詳しくは分からないのですが、今後の展開に期待しています。また、無料カメラが提供される予定、無料ユーザーでも楽しめるように設計されているのも魅力的です。",
      helpfulCount: 3,
    },
    {
      id: 2,
      username: "ユーザーネーム",
      avatar: "/placeholder.svg?height=40&width=40",
      rating: 4,
      date: "2025.06.11",
      content:
        "可愛いデザインにわかりやすいゲームシステム。ブロックチェーンゲーム初心者の方でも始めやすく、楽しめるゲームになっています！",
      helpfulCount: 3,
    },
    {
      id: 3,
      username: "ユーザーネーム",
      avatar: "/placeholder.svg?height=40&width=40",
      rating: 4,
      date: "2025.06.11",
      content:
        "SNPITをやり始めてから素敵な場所に多く訪れるようになり、素敵な瞬間が撮影できて楽しく過ごせて頂いています。\n\nバトルに勝てる写真を撮りに行くのも楽しみですね。",
      helpfulCount: 3,
    },
  ]

  useEffect(() => {
    setBreadcrumb(
      <Breadcrumb className="flex text-[#6D6D6D] pt-2.5 px-5">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/categories/${data?.category?.slug}`} className="text-[#6D6D6D]">
              {data?.category?.name}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage className="font-bold text-[#6D6D6D]">
              {data?.name.en}
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    )
  }, [])
  const handleClickStartGame = (id: string) => {
    startGameDialogRef.current.open({ id });
  };

  return (
    <div className="w-full text-white px-5 py-20">
      <div className="flex flex-col gap-10">
        <div className="container mx-auto sm:px-[30px] px-5 py-10 bg-[#FFFFFF0D] border border-[#1D1A4B] rounded-[10px]">
          <div className="grid lg:grid-cols-2 gap-8 mb-12">
            <div className="flex justify-center">
              <div className="relative w-full">
                <Image
                  src={data?.image}
                  alt={data?.name?.en}
                  width={400}
                  height={500}
                  className="rounded-lg shadow-2xl w-full h-auto object-cover"
                />
              </div>
            </div>
            <div className="space-y-6">
              <div>
                <div className="flex">
                  <div className="text-xs px-[15px] py-[5px] rounded-full border border-white mb-4 ">
                    {data?.category?.name}
                  </div>
                </div>
                <h1 className="lg:text-3xl md:text-2xl text-xl font-bold mb-4">{data?.name?.en}</h1>
                <p className="text-[text-[#BCBCBC]] font-medium leading-relaxed">
                  {data?.short_description}
                </p>
              </div>
              <div className="flex items-center space-x-2 text-[#BCBCBC] font-semibold">
                <div className="flex gap-0.5">
                  {[1, 2, 3, 4].map((star) => (
                    <Star key={star} className="w-[14px] h-[14px] fill-[#F4C51E] text-[#F4C51E]" />
                  ))}
                  <Star className="w-[14px] h-[14px] text-[#F4C51E]" />
                </div>
                <span className="font-semibold">4.0</span>
                <span>（{t('games.review_count')}：10）</span>
              </div>
              <div className="space-y-2">
                <p className="font-medium">
                  <span>{t('games.category')}：</span>{data?.category?.name}
                </p>
                <p className="font-medium">
                  <span>{t('games.chain')}：</span>{data?.chain?.name}
                </p>
              </div>
              <div className="flex sm:flex-row flex-col items-center lg:justify-end justify-start gap-5">
                <Button
                  onClick={() => handleClickStartGame(data?.id)}
                  className="sm:w-auto w-full h-full bg-[#6123DB] hover:bg-[#401596] text-white font-bold px-8 py-3 rounded-lg flex items-center space-x-2">
                  <IoIosPlayCircle className="w-5 h-5" />
                  <span>{t('games.play')}</span>
                </Button>
                <Button
                  className="sm:w-auto w-full h-full !bg-white border border-[#6123DB] text-[#6123DB] font-bold hover:!bg-[#6123DB] hover:text-white px-8 py-3 rounded-lg flex items-center space-x-2 bg-transparent"
                >
                  <Heart className="w-5 h-5" />
                  <span>{t('games.add_to_favorite')}</span>
                </Button>
              </div>
            </div>
          </div>
          <div className="space-y-8">
            <section>
              <h2 className="text-xl font-bold mb-4">{t('games.game_content')}</h2>
              <p className="leading-relaxed">
                {data?.description}
              </p>
            </section>
            <hr className="w-full h-px bg-[#3A3953] border-0"></hr>
            <section>
              <h2 className="text-xl font-bold mb-4">{t('games.feature')}</h2>
              <p className="leading-relaxed">
              {data?.feature}
              </p>
            </section>
            <hr className="w-full h-px bg-[#3A3953] border-0"></hr>
            <section>
              <h2 className="text-xl font-bold mb-4">{t('games.basic_information')}</h2>
              <div className="space-y-2">
                <div dangerouslySetInnerHTML={{ __html: data?.basic_information }} />
              </div>
            </section>
          </div>
        </div>
        <div className="container mx-auto sm:px-[30px] px-5 py-10 bg-[#FFFFFF0D] border border-[#1D1A4B] rounded-[10px]">
          <div className="flex sm:flex-row flex-col justify-between items-center gap-2 mb-8">
            <h1 className="text-2xl font-bold">{t('games.evaluation_review')}</h1>
            <Link href={'/profile/reviews'} prefetch={false}>
              <Button className="sm:w-auto w-full h-full bg-white text-[#6123DB] font-bold border border-[#6123DB] hover:bg-gray-100 px-6 py-2.5 rounded-lg flex items-center space-x-2">
                <Edit className="w-4 h-4" />
                <span>{t('games.write_review')}</span>
              </Button>
            </Link>
          </div>
          <div className="flex flex-col gap-5 mb-8">
            {reviews.map((review, index) => (
              <>
                <ReviewCard key={index} data={review} />
                <hr className="w-full h-px bg-[#3A3953] border-0"></hr>
              </>
            ))}
          </div>
          <div className="flex justify-center">
            <Button className="bg-[#7D69FF] hover:bg-[#5f4fc7] text-white px-8 py-3 rounded-lg">{t('games.more_reviews')}</Button>
          </div>
        </div>
      </div>
      <StartGameDialog ref={startGameDialogRef} />
    </div>
  )
}
export default GameDetail