"use client"

import { Swiper, SwiperSlide } from "swiper/react"
import { Pagination, Autoplay } from "swiper/modules"
import "swiper/css"
import "swiper/css/pagination"
import { Link } from "@/hooks/navigation"
import ImageLoader from "@/components/common/ImageLoader"
import '../style/slide-show.css'

type Props = {
  data: any
}

const SlidesShow = ({ data }: Props) => {
  return (
    <div className="w-full bg-gradient-to-br from-[#2900A6] via-[#7D69FF] to-[#190060] pt-8">
      <Swiper
        modules={[Pagination, Autoplay]}
        spaceBetween={30}
        slidesPerView={2.6}
        centeredSlides={true}
        // autoplay={{
        //   delay: 5000,
        //   disableOnInteraction: false,
        // }}
        pagination={{
          clickable: true,
          renderBullet: (index, className) =>
            '<span class="' + className + ' custom-bullet"></span>',
        }}
        navigation={true}
        loop={true}
        breakpoints={{
          480: {
            slidesPerView: 1,
          },
          640: {
            slidesPerView: 2,
          },
          768: {
            slidesPerView: 2,
          },
          1024: {
            slidesPerView: 3,
          },
          1280: {
            slidesPerView: 3.4,
          },
        }}
        className="game-swiper"
      >
        {data?.map((game: any) => (
          <SwiperSlide key={game.id} className="transition-transform duration-300">
            <Link href={`/games/${game.id}`}>
              <div className="relative overflow-hidden rounded-2xl shadow-2xl">
                <div className="aspect-[16/10] relative">
                  <ImageLoader
                    fill
                    src={game.image}
                    alt={`slide-image-${game.id}`}
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                  <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                    <div className="mb-2">
                      <span className="inline-block text-sm font-medium">
                        おすすめのタイトル
                      </span>
                    </div>
                    <div className="flex items-center gap-3 mb-3">
                      <h3 className="text-2xl font-bold line-clamp-1">{game.name.en}</h3>
                      <span className="px-3 py-1 text-xs font-medium bg-white text-black backdrop-blur-sm rounded-full flex-none">
                        {game.category.name}
                      </span>
                    </div>
                    <p className="text-white font-medium leading-relaxed line-clamp-2">
                      {game.short_description}
                    </p>
                  </div>
                </div>
              </div>
            </Link>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  )
}

export default SlidesShow