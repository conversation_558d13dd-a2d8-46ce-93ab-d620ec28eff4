"use client"
import { useState } from "react"
import { <PERSON>evronLeft, ChevronRight, ArrowRight } from "lucide-react"
import { Swiper, SwiperSlide } from "swiper/react"
import { Navigation, Pagination } from "swiper/modules"
import "swiper/css"
import "swiper/css/navigation"
import "swiper/css/pagination"
import { Link } from "@/hooks/navigation"
import EventCard from "@/components/common/event-card"
import '../style/custom-pagination.css'

type Props = {
  data: any;
  title: string;
  paginationName: string;
  navigationName: string;
  viewMoreLink: string;
}
const GamesSection = ({ data, title, paginationName, navigationName, viewMoreLink }: Props) => {
  const [activeIndex, setActiveIndex] = useState(0)

  return (
    <div className="my-4">
      <div className="flex items-center gap-2 text-white mb-4">
        <h1 className="md:text-2xl text-xl font-bold">{title}</h1>
        <Link href={viewMoreLink} className="text-sm text-gray-500 hover:text-gray-300 transition-colors">
          <ArrowRight className="w-6 h-6" />
        </Link>
      </div>
      <div className="relative">
        <Swiper
          modules={[Navigation, Pagination]}
          spaceBetween={20}
          slidesPerView={4}
          autoplay={{
            delay: 2500,
            disableOnInteraction: false,
          }}
          loop={true}
          onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
          navigation={{
            prevEl: `.${navigationName}-custom-pre`,
            nextEl: `.${navigationName}-custom-next`,
          }}
          pagination={{
            el: `.${paginationName}-custom-pagination`,
            clickable: true,
            renderBullet: (index, className) => {
              return `<span class="${className} custom-bullet" data-index="${index}"></span>`
            },
          }}
          breakpoints={{
            320: {
              slidesPerView: 1,
              spaceBetween: 16,
            },
            640: {
              slidesPerView: 2,
              spaceBetween: 16,
            },
            1024: {
              slidesPerView: 3,
              spaceBetween: 18,
            },
            1280: {
              slidesPerView: 4,
              spaceBetween: 20,
            },
          }}
          className="pb-16"
        >
          {data.map((game: any) => (
            <SwiperSlide key={game.id}>
              <EventCard data={game} />
            </SwiperSlide>
          ))}
        </Swiper>
        <div className="flex items-center gap-5 mt-5">
          <div className="flex gap-3">
            <button className={`${navigationName}-custom-pre w-10 h-10 rounded-full bg-[#3A3A3A] hover:bg-[#252525] text-white flex items-center justify-center transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed`}>
              <ChevronLeft className="w-5 h-5" />
            </button>
            <button className={`${navigationName}-custom-next w-10 h-10 rounded-full bg-[#3A3A3A] hover:bg-[#252525] text-white flex items-center justify-center transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed`}>
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>
          <div className={`${paginationName}-custom-pagination flex items-center gap-2`}></div>
        </div>
      </div>
    </div>
  )
}

export default GamesSection