'use client'
import { useTranslations } from 'next-intl'
import React, { useEffect, useRef } from 'react'
import { useBreadcrumb } from '@/providers/BreadcrumbProvider'
import AllGames from './components/all-game'
import SlidesShow from './components/slides-show'

const HomePage = ({ homeData, categories, chains }: any) => {
  const t = useTranslations('homepage');
  const startGameDialogRef = useRef<any>(null);
  const { setBreadcrumb } = useBreadcrumb();
  const handleClickStartGame = (id: string) => {
    startGameDialogRef.current.open({ id });
  }

  useEffect(() => {
    setBreadcrumb(null)
  }, [])
  return (
    <div>
      {
        homeData?.top?.length > 0 && (
          <SlidesShow data={homeData.top} />
        )
      }
      <AllGames data={homeData} categories={categories} chains={chains} />
    </div>


  )
}

export default HomePage