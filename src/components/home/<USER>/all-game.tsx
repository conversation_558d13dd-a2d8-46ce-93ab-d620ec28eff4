"use client"
import staffCollection from '@/assets/images/slide-game/7bae20207f446bbc229694e6146b8c98f92bcade.jpg'
import Popular from "./popular"
import GameSlide from "./game-slide"
import GamesSection from "./games-section"
import { sliceIntoChunks } from "@/lib/utils"
import { Link } from '@/hooks/navigation'
import Image from 'next/image'
import GridLayout from '@/components/layout/grid-layout'


const AllGames = ({
  data,
  categories,
  chains
}: any) => {
  const categoryData = sliceIntoChunks(data.categories, 4)
  return (
    <GridLayout categories={categories} chains={chains}>
      <GamesSection
        data={data.events}
        title="イベント/キャンペーン"
        paginationName="event"
        navigationName="event"
        viewMoreLink="/events"
      />
      <Popular data={data.popular} />
      <GamesSection
        data={data.latest}
        title="新着／配信予定"
        paginationName="upcoming"
        navigationName="upcoming"
        viewMoreLink="/upcoming"
      />
      <GameSlide image={staffCollection.src} title="スタッフ厳選コレクション（仮）" description="club Xスタッフがおすすめするゲームをご紹介！" nameButton="コレクションを見る" />
      {
        categoryData.map((category: any, index: number) => (
          <div key={index}>
            {
              category.map((item: any, index: number) => (
                <GamesSection
                  key={index}
                  data={item.games}
                  title={item.name}
                  paginationName={item.slug}
                  navigationName={item.slug}
                  viewMoreLink={`/categories/${item.slug}`}
                />
              ))
            }
            <GameSlide image={staffCollection.src} title="スタッフ厳選コレクション（仮）" description="club Xスタッフがおすすめするゲームをご紹介！" nameButton="コレクションを見る" />
          </div>
        ))
      }
    </GridLayout>
  )
}

export default AllGames