"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, ChevronLeft, ChevronRight } from "lucide-react"
import type { Swiper as SwiperType } from "swiper"
import { Swiper, SwiperSlide } from "swiper/react"
import { Navigation, Pagination } from "swiper/modules"
import "swiper/css"
import "swiper/css/navigation"
import "swiper/css/pagination"
import ListCard from "@/components/common/list-card"
import '../style/custom-pagination.css'
import { sliceIntoChunks } from "@/lib/utils"
import { Link } from "@/hooks/navigation"

type Props = {
  data: any;
}

const Popular = ({ data }: Props) => {
  const [swiperRef, setSwiperRef] = useState<SwiperType>()
  const pages = sliceIntoChunks(data, 5)

  return (
    <div className=" text-white">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <h1 className="text-2xl font-bold">人気のゲーム</h1>
          <Link href="/popular" className="text-sm text-gray-500 hover:text-gray-300 transition-colors">
            <ArrowRight className="w-5 h-5" />
          </Link>
        </div>
      </div>
      <div className="overflow-auto relative">
        <Swiper
          onSwiper={setSwiperRef}
          spaceBetween={30}
          slidesPerView={1}
          modules={[Navigation, Pagination]}
          navigation={{
            prevEl: ".popular-custom-prev",
            nextEl: ".popular-custom-next",
          }}
          pagination={{
            el: ".popular-custom-pagination",
            clickable: true,
            renderBullet: (index, className) => {
              return `<span class="${className} custom-bullet" data-index="${index}"></span>`
            },
          }}
          className="gaming-swiper"
          breakpoints={{
            320: {
              slidesPerView: 1,
              spaceBetween: 16,
            },
            640: {
              slidesPerView: 1,
              spaceBetween: 16,
            },
            1024: {
              slidesPerView: 1.2,
              spaceBetween: 18,
            },
            1280: {
              slidesPerView: 1.7,
              spaceBetween: 20,
            },
          }}
        >
          {pages.map((pageData, pageIndex) => (
            <SwiperSlide key={pageIndex}>
              <div className="space-y-4 pr-4">
                {pageData.map((game: any, index: number) => (
                  <ListCard
                    key={`${pageIndex}-${index}`}
                    data={game}
                    index={(index + 1) + (pageIndex * 5)}
                  />
                ))}
              </div>
            </SwiperSlide>
          ))}
          <div className="flex items-center gap-5 mt-5">
            <div className="flex gap-3">
              <button className="popular-custom-prev w-10 h-10 rounded-full bg-[#3A3A3A] hover:bg-[#252525] text-white flex items-center justify-center transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed">
                <ChevronLeft className="w-5 h-5" />
              </button>
              <button className="popular-custom-next w-10 h-10 rounded-full bg-[#3A3A3A] hover:bg-[#252525] text-white flex items-center justify-center transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed">
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
            <div className="popular-custom-pagination flex items-center gap-2"></div>
          </div>
        </Swiper>

      </div>
    </div>
  )
}
export default Popular