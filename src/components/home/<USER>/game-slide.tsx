import { Button } from "@/components/ui/button"
import ImageLoader from "@/components/common/ImageLoader"

type Props = {
  image: string;
  title: string;
  subTitle?: string;
  description?: string;
  nameButton: string;
}

const GameSlide = ({ image, title, subTitle, description, nameButton }: Props) => {
  return (
    <div className="md:mr-20">
      <div className="relative w-full text-white rounded-xl overflow-hidden">
        <ImageLoader fill src={image} alt='image' className='w-full h-auto object-cover' />
        <div className="relative grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          <div className="space-y-6 px-10 py-20">
            <div className="space-y-3">
              {
                subTitle && (
                  <div className="text-lg">{subTitle}</div>
                )
              }
              <h1 className="text-3xl md:text-4xl font-bold text-white leading-tight">
                {title}
              </h1>
              {
                description && (
                  <div className="text-lg">{description}</div>
                )
              }
            </div>
            <Button className="bg-[#1A1A1A] h-full text-white px-8 py-3 rounded-lg font-semibold">
              {nameButton}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default GameSlide