interface Window {
  ethereum?: {
    request: (args: {method: string; params?: any[]}) => Promise<any>;
    on?: (eventName: string, callback: (...args: any[]) => void) => void;
    removeListener?: (eventName: string, callback: (...args: any[]) => void) => void;
    selectedAddress?: string;
    isMetaMask?: boolean;
    isTrust?: boolean;
    isCoinbaseWallet?: boolean;
    isExodus?: boolean;
    isBraveWallet?: boolean;
    providers?: any[];
  };
}

export type PopluarType = {
  id: number;
  title: string;
  platform: string;
  category: string;
  rating: number;
  image: string;
};

export type GamesType = {
  id: number;
  title: string;
  description?: string;
  image: string;
  chain: string;
  category?: string;
};
