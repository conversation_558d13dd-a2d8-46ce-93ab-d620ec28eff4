import {getRequestConfig} from 'next-intl/server';
import {routing} from '@/hooks/navigation';

export default getRequestConfig(async ({requestLocale}) => {
  let locale = (await requestLocale) as any;
  if (!locale || !routing.locales.includes(locale)) {
    locale = routing.defaultLocale;
  }
  return {
    messages: (await import(`./locales/${locale}.json`)).default,
    timeZone: 'Asia/Tokyo',
    locale,
  };
});
