'use server';
import { GET, fetchJson } from '@/services/request';

export const fetchHome = async () => {
  try {
    const resp = await fetchJson(GET('/home'));
    const data = resp.data
    return { status: 'success', data: data.data }
  } catch (error: any) {
    return { status: 'error', message: error?.response?.data?.message, data: [] }
  }
}

export const fetchBanner = async (params: any) => {
  try {
    const { data } = await fetchJson(GET('/banners', params));
    return { status: 'success', banners: data.data };
  } catch (error: any) {
    console.log(error);
    return { status: 'error', data: [] };
  }
};

export const fetchPickUpGames = async (params: any) => {
  try {
    const resp = await fetchJson(GET('/games/pickup', params));
    const data = resp.data
    return { status: 'success', pickUpGames: data.data };
  } catch (error: any) {
    console.log(error);
    return { status: 'error', data: [] };
  }
};

export const fetchCategories = async ({ params }: any) => {
  try {
    const resp = await fetchJson(GET('/categories', params));
    const data = resp?.data?.data;
    return { status: 'success', data }
  } catch (error: any) {
    return { status: 'error', message: error?.response?.data?.message, data: [] }
  }
}

export const fetchChains = async ({ params }: any) => {
  try {
    const resp = await fetchJson(GET('/chains', params));
    const data = resp?.data?.data;
    return { status: 'success', data }
  } catch (error: any) {
    return { status: 'error', message: error?.response?.data?.message, data: [] }
  }
}

export const fetchGamesCategories = async (params: any) => {
  try {
    const resp = await fetchJson(GET(`/categories/${params?.slug}/games`, params));
    const data = resp?.data
    return { status: 'success', data: data?.data, meta: data?.meta }
  } catch (error: any) {
    return { status: 'error', message: error?.response?.data?.message, data: [] }
  }
}

export const fetchAllGames = async (params: any) => {
  try {
    const resp = await fetchJson(GET('/games', params))
    const data = resp?.data
    return { status: 'success', data: data?.data, meta: data?.meta }
  } catch (error: any) {
    return { status: 'error', message: error?.response?.data?.message, data: [] }
  }
}

export const fetchGamesChains = async (params: any) => {
  try {
    const resp = await fetchJson(GET(`/chains/${params?.slug}/games`, params));
    const data = resp?.data
    return { status: 'success', data: data?.data, meta: data?.meta }
  } catch (error: any) {
    return { status: 'error', message: error?.response?.data?.message, data: [] }
  }
}

export const fetchEvents = async (params: any) => {
  try {
    const resp = await fetchJson(GET('/games/event', params));
    const data = resp?.data
    return { status: 'success', data: data?.data, meta: data?.meta }
  } catch (error: any) {
    return { status: 'error', message: error?.response?.data?.message, data: [] }
  }
}

export const fetchLatestGames = async (params: any) => {
  try {
    const resp = await fetchJson(GET('/games/latest', params));
    const data = resp?.data
    return { status: 'success', data: data?.data, meta: data?.meta }
  } catch (error: any) {
    return { status: 'error', message: error?.response?.data?.message, data: [] }
  }
}

export const fetchRanking = async (params: any) => {
  try {
    const resp = await fetchJson(GET('/games/popular', params));
    const data = resp?.data
    return { status: 'success', data: data?.data, meta: data?.meta }
  } catch (error: any) {
    return { status: 'error', message: error?.response?.data?.message, data: [] }
  }
}
