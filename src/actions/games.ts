'use server';
import {fetchJson, GET, POST} from '@/services/request';
import {getCookies} from './auth';

export const handleStartGame = async (id: string) => {
  try {
    const cookies = await getCookies();
    const token = cookies?.value;
    if (!token) {
      return {status: 'error', data: {}};
    }
    const {data} = await fetchJson(POST(`/games/${id}/start`, {}, token));
    return {status: 'success', url: data.url};
  } catch (error: any) {
    return {status: 'error', message: error?.response?.data?.message};
  }
};

export const fetchGame = async (id: any) => {
  try {
    const resp = await fetchJson(GET(`/games/${id}`));
    const data = resp?.data
    return { status: 'success', data: data?.data }
  } catch (error: any) {
    return { status: 'error', message: error?.response?.data?.message, data: [] }
  }
}
