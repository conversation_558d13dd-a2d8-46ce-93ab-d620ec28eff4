'use server';
import {DELETE, GET, POST, fetchJson, removeAuthToken, setAuthToken} from '@/services/request';
import {cookies} from 'next/headers';

export const fetchProfile = async () => {
  try {
    const cookies = await getCookies();
    const token = cookies?.value;
    if (!token) {
      return {status: 'error', data: {}};
    }
    const resp = await fetchJson(GET('/auth/account', {}, token));
    const data = resp.data.data;
    return {status: 'success', data};
  } catch (error: any) {
    return {status: 'error', message: error?.response?.data?.error};
  }
};

export const verifyCode = async (params: any) => {
  try {
    const {
      data: {data, message},
    } = await fetchJson(POST('/auth/verify-user-code', params));
    return {status: 'success', data, message};
  } catch (error: any) {
    return {
      status: 'error',
      message: error?.response?.data?.error?.message,
      errors: error?.response?.data?.error?.errors,
    };
  }
};

export const fetchNounce = async (params: any) => {
  try {
    const resp = await fetchJson(POST('/auth/generate-nounce', params));
    const data = resp.data;
    return {status: 'success', data};
  } catch (error: any) {
    console.log(error);
    return {status: 'error', message: error?.response?.data?.error?.message};
  }
};

export const fetchWallets = async (address: string, signature: string) => {
  try {
    const resp = await fetchJson(POST('/auth/verify-wallet', {signature, address}));
    const data = resp?.data?.data;
    const token = data?.token?.token;
    setAuthToken(token);
    setCookies(token);
    return {status: 'success', data};
  } catch (error: any) {
    console.log(error);
    return {status: 'error', message: error?.response?.data?.error?.message};
  }
};

export const fetchLogout = async () => {
  try {
    const cookies = await getCookies();
    const token = cookies?.value;
    if (token) {
      fetchJson(DELETE('/auth/logout', {}, token));
    }
    removeAuthToken();
    removeCookies();
    return {status: 'success'};
  } catch (err: any) {
    return {status: 'error', message: err?.response?.data?.error?.message};
  }
};

export async function setCookies(token: any) {
  const decodedJwt = JSON.parse(atob(token.split('.')[1]));
  const expires = decodedJwt.exp * 1000;
  cookies().set({
    name: process.env.COOKIE_NAME as string,
    value: token,
    path: '/',
    expires,
  });
}
export async function getCookies() {
  const token = cookies().get(process.env.COOKIE_NAME as string);

  return token;
}

export async function removeCookies() {
  cookies().delete(process.env.COOKIE_NAME as string);
}
