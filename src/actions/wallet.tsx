'use server';
import {POST, fetchJson} from '@/services/request';
import {getCookies} from './auth';

export const handleTopUp = async (params: any) => {
  try {
    const cookies = await getCookies();
    const token = cookies?.value;
    if (!token) {
      return {status: 'errpr', data: {}};
    }
    const resp = await fetchJson(POST('/wallets/topup', params, token));
    const data = resp.data;
    return {status: 'success', data};
  } catch (error: any) {
    return {status: 'error', message: error?.response?.data?.message};
  }
};

export const handleSwap = async (params: any) => {
  try {
    const cookies = await getCookies();
    const token = cookies?.value;
    if (!token) {
      return {status: 'errpr', data: {}};
    }
    const resp = await fetchJson(POST('/wallets/swap', params, token));
    const data = resp.data;
    return {status: 'success', data};
  } catch (error: any) {
    return {status: 'error', message: error?.response?.data?.message};
  }
};
