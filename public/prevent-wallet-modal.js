// This script runs immediately to prevent wallet modals on login page
(function() {
  'use strict';
  
  // Only run on login page
  if (!window.location.pathname.includes('/auth/login')) {
    return;
  }
  
  console.log('[prevent-wallet-modal.js] Smart wallet blocking active on login page');
  
  // Only clear auto-connect keys, not ALL storage
  const clearAutoConnect = () => {
    const autoConnectKeys = [
      'wagmi.recentConnectorId',
      'wagmi.connected',
      '@appkit/connected_connector',
      '@w3m/connected_connector',
      'wc@2:client:0.3//session',
      '@appkit/recent'
    ];
    
    autoConnectKeys.forEach(key => {
      if (localStorage.getItem(key)) {
        console.log('[prevent-wallet-modal.js] Clearing:', key);
        localStorage.removeItem(key);
      }
    });
  };
  
  clearAutoConnect();
  
  // Track user interactions
  let isUserInitiated = false;
  let lastUserAction = 0;
  
  // Monitor user clicks on wallet buttons
  document.addEventListener('click', function(e) {
    const target = e.target;
    const isWalletAction = 
      target.closest('button')?.textContent?.toLowerCase().includes('wallet') ||
      target.closest('button')?.textContent?.toLowerCase().includes('trust') ||
      target.closest('button')?.textContent?.toLowerCase().includes('metamask') ||
      target.closest('[data-testid*="wallet"]') ||
      target.closest('[class*="wallet"]');
    
    if (isWalletAction) {
      console.log('[prevent-wallet-modal.js] User clicked wallet button');
      isUserInitiated = true;
      lastUserAction = Date.now();
      // Reset after 5 seconds
      setTimeout(() => { isUserInitiated = false; }, 5000);
    }
  }, true);
  
  // Trust Wallet specific clearing
  const trustWalletKeys = [
    'wagmi.com.trustwallet.app.disconnected',
    'trust.disconnected',
    'wagmi.com.trustwallet.app',
    'WALLETCONNECT_DEEPLINK_CHOICE',
  ];
  
  trustWalletKeys.forEach(key => {
    if (localStorage.getItem(key)) {
      console.log('[prevent-wallet-modal.js] Removing Trust Wallet key:', key);
      localStorage.removeItem(key);
    }
  });
  
  // Only clear auto-connect states on initial load
  const autoConnectPatterns = [
    'recentConnectorId', 'connectionId', 'connected',
    'wagmi.store', 'wagmi.cache'
  ];
  
  // Clear auto-connect storage only
  Object.keys(localStorage).forEach(key => {
    if (autoConnectPatterns.some(pattern => key.includes(pattern))) {
      // Check if it's Trust Wallet related
      const value = localStorage.getItem(key);
      if (value && (value.includes('trust') || value.includes('com.trustwallet'))) {
        console.log('[prevent-wallet-modal.js] Clearing Trust auto-connect key:', key, value);
        localStorage.removeItem(key);
      }
    }
  });
  
  // Don't override getItem - it's too aggressive
  // Let the React components handle the blocking
  
  // Don't override setItem either - too aggressive
  
  // Clear ethereum provider state
  if (window.ethereum) {
    try {
      if (window.ethereum.selectedAddress) {
        window.ethereum.selectedAddress = null;
      }
      if (window.ethereum._state) {
        window.ethereum._state.accounts = [];
        window.ethereum._state.isConnected = false;
      }
    } catch (e) {}
  }
})();