// next.config.mjs

import withNextIntl from "next-intl/plugin";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const nextConfig = {
  env: {
    WALLET_CONNECT_PROJECT_ID: process.env.WALLET_CONNECT_PROJECT_ID,
    APP_ENV: process.env.APP_ENV,
    API_KEY_AUTHORIZE: process.env.API_KEY_AUTHORIZE,
    COOKIE_NAME: process.env.COOKIE_NAME,
    ENCRYPT_KEY: process.env.ENCRYPT_KEY,
    BASE_URL: process.env.BASE_URL,
    CLUBX_USDT_ADDRESS: process.env.CLUBX_USDT_ADDRESS,
    CLUBX_PURCHASE_ADDRESS: process.env.CLUBX_PURCHASE_ADDRESS,
    MODE: process.env.MODE,
    BASE_RPC_URL: process.env.BASE_RPC_URL
  },

  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "*",
      },
      {
        protocol: "http",
        hostname: "*",
      },
    ],
  },

  swcMinify: false,

  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Handle Coinbase Wallet SDK HeartbeatWorker issue
      config.optimization.minimizer = config.optimization.minimizer.map(minimizer => {
        if (minimizer.constructor.name === 'TerserPlugin') {
          minimizer.options.terserOptions = {
            ...minimizer.options.terserOptions,
            compress: {
              ...minimizer.options.terserOptions?.compress,
              unused: false,
            },
            mangle: {
              ...minimizer.options.terserOptions?.mangle,
              reserved: ['export'],
            },
          };
        }
        return minimizer;
      });

      // Also add the loader approach
      config.module.rules.push({
        test: /HeartbeatWorker\.js$/,
        use: [
          {
            loader: path.resolve(__dirname, 'webpack-loaders/fix-worker-loader.js'),
          }
        ]
      });
    }
    return config;
  },
};

export default withNextIntl("./src/i18n.ts")(nextConfig);
